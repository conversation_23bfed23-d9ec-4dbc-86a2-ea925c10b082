using System;
using System.Collections.Generic;
using System.Text;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer.APDU;
using VAS.Devices.CardTerminals.Protocols.ZVT.TransportLayer;
using VAS.Devices.CardTerminals.Commands;
using System.Xml;
using VAS.Diagnostic.Log;

namespace VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer.Commands
{
    public class EndOfDayCommand : CommandBase<EndOfDayApdu, CommandResult>, IEndOfDayCommand
    {
        private Logger _log = LogManager.Global.GetLogger("VAS");

        
        public EndOfDayCommand(IZvtTransport transport, ZVTCommandEnvironment commandEnvironment)
            : base(transport, commandEnvironment)
        {
            _apdu = new EndOfDayApdu();
        }

        public override CommandResult Execute()
        {
            try
            {
                CommandResult result = new CommandResult();
                result.Success = true;

                if(_environment.RaiseAskOpenConnection())
                    _transport.OpenConnection();

                ApduCollection apdus = _commandTransmitter.TransmitAPDU(_apdu);
                CheckForAbortApdu(result, apdus);
                result.PrintDocuments = _commandTransmitter.PrintDocuments;
                return result;
            }
            finally
            {
                if(_environment.RaiseAskCloseConnection())
                    _transport.CloseConnection();
            }
        }

        public override void ReadSettings(XmlElement settings)
        {
            
        }

    }
}
