using System;
using System.Collections.Generic;
using System.Text;
using VAS.Devices.CardTerminals.Protocols.ZVT.TransportLayer;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer.APDU;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer.ApduHandlerDefinitions;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer.Parameters;
using VAS.Devices.CardTerminals.PrintSupport;

namespace VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer
{
    /// <summary>
    /// Transmits the given APDU and waits for a specified "magic apdu" ;-)
    /// </summary>
    public class MagicResponseCommandTransmitter : ICommandTransmitter
    {
        public event Action<IntermediateStatusApduResponse> StatusReceived;

        public delegate bool PacketReceivedDelegate(IZvtApdu transmittedApdu, IZvtApdu responseApdu);


        /// <summary>
        /// Checks if the current packet is the completion packet
        /// </summary>
        public event PacketReceivedDelegate IsCompletionPacket;

        /// <summary>
        /// Transport implementation to use
        /// </summary>
        private IZvtTransport _transport;

        private List<IApduHandler> _apduHandlers = new List<IApduHandler>();

        private PrintApduHandler _printApduHandler;

        private bool _hasReceivedIntermediateStatus = false; // Track if we received intermediate status

        /// <summary>
        /// Enable compatibility mode for new terminals that don't send intermediate status packets
        /// </summary>
        public static bool EnableNewTerminalCompatibility { get; set; } = true;

        public IPrintDocument[] PrintDocuments
        {
            get { return _printApduHandler.PrintDocuments; }
        }

        public MagicResponseCommandTransmitter(IZvtTransport transport)
        {
            _transport = transport;
             _printApduHandler = new PrintApduHandler(transport);
            _apduHandlers.Add(new AckSenderApduHandler(_transport));
            _apduHandlers.Add(new IntermediateStatusApduHandler(_transport, IntermediateStatusReceived));
            _apduHandlers.Add(_printApduHandler);
        }

        private void IntermediateStatusReceived(IntermediateStatusApduResponse status)
        {
            if (StatusReceived != null)
                StatusReceived(status);
        }

        /// <summary>
        /// Finds the handlers of the specified response apdu
        /// </summary>
        /// <param name="apdu"></param>
        /// <returns></returns>
        private void CallResponseApduHandlers(IZvtApdu requestApdu, IZvtApdu responseApdu)
        {
            foreach (IApduHandler handler in _apduHandlers)
            {
                if (handler.IsCompatibleHandler(responseApdu))
                    handler.Process(requestApdu, responseApdu);
            }
        }

        #region ICommandTransmitter Members

        public event Action<IZvtApdu> ResponseReceived;

        public ApduCollection TransmitAPDU(IZvtApdu apdu)
        {
            // Reset the intermediate status flag for each new command
            _hasReceivedIntermediateStatus = false;

            foreach (IApduHandler apduHandler in _apduHandlers)
                apduHandler.StartCommand();


            _transport.Transmit(_transport.CreateTpdu(apdu));

            ApduCollection responses = new ApduCollection();

            while (true)
            {
                byte[] apduData = _transport.ReceiveResponsePacket();
                byte[] apduCopy = new byte[apduData.Length];
                Array.Copy(apduData, apduCopy, apduData.Length);
                IZvtApdu responseApdu = ApduResponse.Create(apduData);

                if (responseApdu == null)
                    throw new ArgumentException("Could not retrieve response");

                if (this.ResponseReceived != null)
                    ResponseReceived(responseApdu);

                responses.Add(responseApdu);

                // Track if we received an intermediate status packet
                if (responseApdu is IntermediateStatusApduResponse)
                {
                    _hasReceivedIntermediateStatus = true;
                }

                CallResponseApduHandlers(apdu, responseApdu);

                if (IsCompletionPacket == null && InternalIsCompletionPacket(apdu, responseApdu))
                {
                    break;
                }
                else if(IsCompletionPacket != null && IsCompletionPacket(apdu, responseApdu))
                    break;
                
            }

            return responses;
        }

        private bool InternalIsCompletionPacket(IZvtApdu transmittedApdu, IZvtApdu responseApdu)
        {
            if (transmittedApdu.SendsCompletionPacket)
            {
                byte[] apduData = responseApdu.GetRawApduData();

                if (apduData[0] == 0x80 && apduData[1] == 0x00)
                {
                    _transport.MasterMode = false;
                }

                // Handle completion packets (060F)
                if (responseApdu is CompletionApduResponse || responseApdu is AbortApduResponse ||
                    (responseApdu is StatusApdu && ((StatusApdu)responseApdu).Status != StatusCodes.ErrorIDEnum.NoError))
                {
                    _transport.MasterMode = true;

                    // For new terminals that skip intermediate status packets,
                    // we need to simulate the missing intermediate status when we get a completion packet
                    if (EnableNewTerminalCompatibility && responseApdu is CompletionApduResponse)
                    {
                        SimulateMissingIntermediateStatus(transmittedApdu, responseApdu as CompletionApduResponse);
                    }

                    return true;
                }

                return false;
            }
            else
            {
                return true;
            }
        }

        /// <summary>
        /// Simulates intermediate status for new terminals that skip the 04ff packet
        /// </summary>
        private void SimulateMissingIntermediateStatus(IZvtApdu transmittedApdu, CompletionApduResponse completionApdu)
        {
            // Only simulate if we haven't received any intermediate status packets
            if (_hasReceivedIntermediateStatus)
                return;

            // For payment commands, simulate a PaymentApproved status if the completion is successful
            if (transmittedApdu is AuthorizationApdu)
            {
                // Determine the appropriate status based on the completion packet
                byte statusCode = DetermineStatusFromCompletion(completionApdu);

                // Create a simulated intermediate status
                byte[] simulatedApduData = new byte[] { 0x04, 0xFF, 0x01, statusCode };
                IntermediateStatusApduResponse simulatedStatus = new IntermediateStatusApduResponse(simulatedApduData);

                // Log the simulation for debugging
                System.Diagnostics.Debug.WriteLine($"[Compatibility Mode] Simulating missing intermediate status packet 04FF01{statusCode:X2}");

                // Trigger the status callback to maintain compatibility
                if (StatusReceived != null)
                {
                    StatusReceived(simulatedStatus);
                }

                // Mark that we've now provided intermediate status
                _hasReceivedIntermediateStatus = true;
            }
        }

        /// <summary>
        /// Determines the appropriate intermediate status code based on completion packet
        /// </summary>
        private byte DetermineStatusFromCompletion(CompletionApduResponse completionApdu)
        {
            // Default to PaymentApproved for successful completion
            byte statusCode = 0x1C; // PaymentApproved

            try
            {
                // Check if there's a status byte parameter in the completion packet
                var statusParam = completionApdu.FindParameter<CompletionStatusByteParameter>(CompletionApduResponse.ParameterTypeEnum.StatusByte);

                if (statusParam != null)
                {
                    // If there are any error conditions in the status byte, use a different status
                    if (statusParam.InitialisationNecessary || statusParam.DiagnosisNecessary)
                    {
                        statusCode = 0x03; // ActionNotPossible
                    }
                    else
                    {
                        // Use PaymentApproved2 for alternative success indication
                        statusCode = 0xA0; // PaymentApproved2
                    }
                }
            }
            catch
            {
                // If we can't parse the completion packet, default to PaymentApproved
                statusCode = 0x1C;
            }

            return statusCode;
        }

        #endregion
    }
}
