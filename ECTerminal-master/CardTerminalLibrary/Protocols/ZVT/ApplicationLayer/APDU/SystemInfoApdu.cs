using System;
using System.Collections.Generic;
using System.Text;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer.Parameters;

namespace VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer.APDU
{
    public class SystemInfoApdu : ApduBase
    {
        protected override byte[] ByteControlField
        {
            get { return new byte[] { 0x0f, 0x11 }; }
        }

        public SystemInfoApdu()
        {
            _parameters.Add(new BCDNumberParameter(0, 0, 0, 0, 0, 0));
        }

    }
}
