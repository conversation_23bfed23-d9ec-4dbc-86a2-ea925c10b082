//------------------------------------------------------------------------------
// <auto-generated>
//     Dieser Code wurde von einem Tool generiert.
//     Laufzeitversion:4.0.30319.42000
//
//     Änderungen an dieser Datei können falsches Verhalten verursachen und gehen verloren, wenn
//     der Code erneut generiert wird.
// </auto-generated>
//------------------------------------------------------------------------------

namespace VAS.Devices.CardTerminals.Protocols.ZVT {
    using System;
    
    
    /// <summary>
    ///   Eine stark typisierte Ressourcenklasse zum Suchen von lokalisierten Zeichenfolgen usw.
    /// </summary>
    // Diese K<PERSON>e w<PERSON> von der StronglyTypedResourceBuilder automatisch generiert
    // -Klasse über ein Tool wie ResGen oder Visual Studio automatisch generiert.
    // Um einen Member hinzuzufügen oder zu entfernen, bearbeiten Sie die .ResX-Datei und führen dann ResGen
    // mit der /str-Option erneut aus, oder Sie erstellen Ihr VS-Projekt neu.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class LangDe {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal LangDe() {
        }
        
        /// <summary>
        ///   Gibt die zwischengespeicherte ResourceManager-Instanz zurück, die von dieser Klasse verwendet wird.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("VAS.Devices.CardTerminals.Protocols.ZVT.LangDe", typeof(LangDe).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Überschreibt die CurrentUICulture-Eigenschaft des aktuellen Threads für alle
        ///   Ressourcenzuordnungen, die diese stark typisierte Ressourcenklasse verwenden.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Maximale Anzahl an falschen PIN eingaben erreicht ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse__PinTryLimitExceeded {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_ PinTryLimitExceeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Funktion nicht durchführbar ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_ActionNotPossible {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_ActionNotPossible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Automatischer Kassenschnitt wird durchgeführt ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_AutomaticEndOfDay {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_AutomaticEndOfDay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Karte abgelaufen ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_CardExpired {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_CardExpired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Karte ungültig ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_CardInvalid {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_CardInvalid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Karte unzulässig ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_CardNotPermitted {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_CardNotPermitted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Karte nicht lesbar ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_CardNotReadable {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_CardNotReadable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Unbekannte Karte ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_CardUnknown {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_CardUnknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Pin falsch ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_IncorrectPin {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_IncorrectPin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Karte einführen ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_InsertCard {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_InsertCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Display von PIN-Pad beachten ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_NoteDisplayPinPad {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_NoteDisplayPinPad", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Display von PIN-Pad beachten ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_NoteDisplayPinPad2 {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_NoteDisplayPinPad2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die ---- Zahlung erfolgt ---- ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_PaymentApproved {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_PaymentApproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die ---- Zahlung erfolgt ---- ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_PaymentApproved2 {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_PaymentApproved2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Warten auf EC-Terminal ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_PleaseWait {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_PleaseWait", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Vorgang abgebrochen ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_ProcessCancelled {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_ProcessCancelled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Arbeite... ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_Processing {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_Processing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Terminal sendet Nachbuchungen ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_PTSendingPostBookings {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_PTSendingPostBookings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Terminal führt Auto-Stornierung durch ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_PTSendsAutoReversal {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_PTSendsAutoReversal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Warte auf Bestätigung des Betrages... ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_PTWaitingForAmountConfirmation {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_PTWaitingForAmountConfirmation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Karte entfernen ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_RemoveCard {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_RemoveCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Terminal wartet auf FEP Antwort ähnelt.
        /// </summary>
        internal static string IntermediateStatusApduResponse_WaitingForFEPResponse {
            get {
                return ResourceManager.GetString("IntermediateStatusApduResponse_WaitingForFEPResponse", resourceCulture);
            }
        }
    }
}
