<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{9E899822-A4D4-47D4-9DAA-9E8279F2201E}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>VAS.Devices.CardTerminals</RootNamespace>
    <AssemblyName>CardTerminalLibrary</AssemblyName>
    <TargetFrameworkVersion>v4.8.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>ecterminalkey.snk</AssemblyOriginatorKeyFile>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <TargetFrameworkProfile />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <RegisterForComInterop>true</RegisterForComInterop>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <RegisterForComInterop>true</RegisterForComInterop>
    <GenerateSerializationAssemblies>On</GenerateSerializationAssemblies>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>bin\x86\Release\</OutputPath>
    <RegisterForComInterop>true</RegisterForComInterop>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Commands\ICommandEnvironment.cs" />
    <Compile Include="Commands\IEndOfDayCommand.cs" />
    <Compile Include="Commands\IInitialisationCommand.cs" />
    <Compile Include="Commands\Data\InitialisationResult.cs" />
    <Compile Include="Commands\IReportCommand.cs" />
    <Compile Include="Commands\IResetCommand.cs" />
    <Compile Include="Commands\IReversalCommand.cs" />
    <Compile Include="Commands\Data\PaymentResult.cs" />
    <Compile Include="Commands\Data\CommandResult.cs" />
    <Compile Include="Commands\IPaymentCommand.cs" />
    <Compile Include="Commands\ICommand.cs" />
    <Compile Include="Commands\Data\IData.cs" />
    <Compile Include="Commands\Data\IntermediateStatus.cs" />
    <Compile Include="Common\ByteBuffer.cs" />
    <Compile Include="Common\ByteHelpers.cs" />
    <Compile Include="Common\XmlHelper.cs" />
    <Compile Include="Communication\ICommunication.cs" />
    <Compile Include="Communication\SerialComm.cs" />
    <Compile Include="ConfigFactory.cs" />
    <Compile Include="Data\Config.cs" />
    <Compile Include="Ipay.cs" />
    <Compile Include="Log\AbstractLogger.cs" />
    <Compile Include="Log\BuiltinLoggerFactory.cs" />
    <Compile Include="Log\CircularListLogger.cs" />
    <Compile Include="Log\ForwardingLogger.cs" />
    <Compile Include="Log\Interfaces.cs" />
    <Compile Include="Log\LoggerFactory.cs" />
    <Compile Include="Log\LogManager.cs" />
    <Compile Include="Log\LogUtils.cs" />
    <Compile Include="Log\TeeLogger.cs" />
    <Compile Include="Log\TextLogger.cs" />
    <Compile Include="pay.cs" />
    <Compile Include="PrintSupport\IPrintDocument.cs" />
    <Compile Include="PrintSupport\IPrintLine.cs" />
    <Compile Include="PrintSupport\IPrintText.cs" />
    <Compile Include="PrintSupport\PrintDocument.cs" />
    <Compile Include="PrintSupport\PrintLine.cs" />
    <Compile Include="PrintSupport\PrintText.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Protocols\ProtocolFactory.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\ApduHandlerDefinitions\AckSenderApduHandler.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\ApduHandlerDefinitions\IntermediateStatusApduHandler.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\ApduHandlerDefinitions\IApduHandler.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\ApduHandlerDefinitions\PrintApduHandler.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\ActivateCardReaderApdu.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\ApduBase.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\ApduCollection.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\AuthorizationApdu.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\CommandHelpers.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\CompletionApduResponse.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\AbortApduResponse.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\EndOfDayApdu.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\IntermediateStatusApduResponse.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\PrintLineApduResponse.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\ResetApdu.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\ReversalApdu.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\SystemInfoApdu.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\DiagnosisApdu.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\InitialisationApdu.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\LoadParameterHelper.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\ReadCardApdu.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\ReportApdu.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\StatusApdu.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\StatusCodes.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\StatusInformationApdu.cs" />
    <Compile Include="Commands\IDiagnosisCommand.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Commands\ResetCommand.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\TerminalCompatibilityTest.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\ZVTCommandEnvironment.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Commands\AuthorizationCommand.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Commands\CommandBase.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Commands\EndOfDayCommand.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Commands\ReportCommand.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Commands\ReversalCommand.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Commands\InitialisationCommand.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Commands\NetworkDiagnosisCommand.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Commands\RegistrationCommand.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\ControlField.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\ICommandTransmitter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\MagicResponseCommandTransmitter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\AsciiFixedSizeParameter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\BCDNumberParameter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\BitConfigParameter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\RegistrationApdu.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\AsciiLVarParameter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\CompletionStatusByteParameter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\FixedSizeParam.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\LVarBCDNumberParameter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\LVarParameter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\ParameterEncodingHelper.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\StatusAuthorisationAttributeParam.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\StatusExpDateParameter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\CurrencyCodeParameter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\StatusPanEfId.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\StatusPaymentTypeParam.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\StatusTimeParameter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\IParameter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\IZvtApdu.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\APDU\ApduResponse.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\OptionalParameter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\PaymentTypeParam.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\PrefixedParameter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\RegistrationConfigByteParameter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\RegistrationServiceByteParameter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\SingleByteParameter.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\StatusInformationResultCode.cs" />
    <Compile Include="Protocols\ZVT\ApplicationLayer\Parameters\StatusDateParameter.cs" />
    <Compile Include="Protocols\ZVT\LangDe.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>LangDe.resx</DependentUpon>
    </Compile>
    <Compile Include="Protocols\ZVT\ResourceHelper.cs" />
    <Compile Include="Protocols\ZVT\TransportLayer\CRCChecksum.cs" />
    <Compile Include="Protocols\ZVT\TransportLayer\IZvtTransport.cs" />
    <Compile Include="Protocols\ZVT\TransportLayer\NetworkTpdu.cs" />
    <Compile Include="Protocols\ZVT\TransportLayer\NetworkTransport.cs" />
    <Compile Include="Protocols\ZVT\TransportLayer\RS232Tpdu.cs" />
    <Compile Include="Protocols\ZVT\TransportLayer\RS232Transport.cs" />
    <Compile Include="Protocols\ZVT\TransportLayer\IZvtTpdu.cs" />
    <Compile Include="Protocols\ZVT\TransportLayer\TpduBase.cs" />
    <Compile Include="Protocols\ZVT\TransportLayer\TransportException.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="FodyWeavers.xml" />
    <Content Include="Protocols\ZVT\SampleConfiguration.xml" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Protocols\ZVT\LangDe.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>LangDe.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <None Include="ecterminalkey.snk">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\packages\Fody.2.0.6\build\portable-net+sl+win+wpa+wp\Fody.targets" Condition="Exists('..\packages\Fody.2.0.6\build\portable-net+sl+win+wpa+wp\Fody.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Fody.2.0.6\build\portable-net+sl+win+wpa+wp\Fody.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Fody.2.0.6\build\portable-net+sl+win+wpa+wp\Fody.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>