using System;
using System.Collections.Generic;
using System.Text;
using VAS.Devices.CardTerminals.PrintSupport;
using System.Xml;
using VAS.Services.Utils;

namespace VAS.Devices.CardTerminals.Commands
{
    public class CommandResult
    {
        /// <summary>
        /// Indicates if the Operation was successful
        /// </summary>
        protected bool _success;

        /// <summary>
        /// Error code for error tracement, in terms of the used provider
        /// </summary>
        protected int? _protocolSpecificErrorCode;


        /// <summary>
        /// Error description for simpler error tracement
        /// </summary>
        protected string _protocolSpecificErrorDescription;

        /// <summary>
        /// Contains all print documents that where generated by the command
        /// </summary>
        protected IPrintDocument[] _printDocuments;

        public bool Success
        {
            get { return _success; }
            set { _success = value; }
        }

        public int? ProtocolSpecificErrorCode
        {
            get { return _protocolSpecificErrorCode; }
            set { _protocolSpecificErrorCode = value; }
        }

        public string ProtocolSpecificErrorDescription
        {
            get { return _protocolSpecificErrorDescription; }
            set { _protocolSpecificErrorDescription = value; }
        }

        public IPrintDocument[] PrintDocuments
        {
            get { return _printDocuments; }
            set { _printDocuments = value; }
        }

        public CommandResult()
        {
        }

        public CommandResult(bool success, int? protocolSpecificErrorCode, string protocolSpecificErrorMessage)
        {
            _success = success;
            _protocolSpecificErrorCode = protocolSpecificErrorCode;
            _protocolSpecificErrorDescription = protocolSpecificErrorMessage;
        }

        public virtual void SerializeToXml(XmlElement rootNode)
        {
            XmlHelper.WriteBool(rootNode, "Success", Success);
            XmlHelper.WriteInt(rootNode, "ProtocolSpecificErrorCode", _protocolSpecificErrorCode);
            XmlHelper.WriteString(rootNode, "ProtocolSpecificErrorDescription", _protocolSpecificErrorDescription);

            if (_printDocuments != null)
            {
                foreach (IPrintDocument document in _printDocuments)
                {
                    XmlElement documentRoot = (XmlElement)rootNode.AppendChild(rootNode.OwnerDocument.CreateElement("Document"));
                    document.SerializeToXml(documentRoot);
                }
            }

        }
    }
}
