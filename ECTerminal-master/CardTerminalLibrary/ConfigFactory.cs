using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Xml;


namespace VAS.Devices.CardTerminals
{
    [Serializable(), ClassInterface(ClassInterfaceType.AutoDual), ComVisible(true)]

    public class ConfigFactory
    {
        private XmlDocument xmlConfig;
        string path;

        public void _ConfigFactory(string path)
        {
            this.path = path;

        }

        public string initConfig(string path)
        {

            //    var reader = new XmlTextReader(@path);
            var config = new XmlDocument();

            config.PreserveWhitespace = false;
            config.Load(@path);
            // reader = config.FirstChild;
            XmlNode node = config.FirstChild;
            using (var stringWriter = new StringWriter())
            using (var xmlTextWriter = XmlWriter.Create(stringWriter))
            {
                config.WriteTo(xmlTextWriter);
                xmlTextWriter.Flush();
                return stringWriter.GetStringBuilder().ToString();
            }
        }


    }
}
