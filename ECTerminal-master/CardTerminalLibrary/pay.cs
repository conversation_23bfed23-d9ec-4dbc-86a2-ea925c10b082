using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using VAS.Devices.CardTerminals.Protocols.ZVT.TransportLayer;
using VAS.Diagnostic.Log;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer;
using System.IO.Ports;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer.Commands;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer.Parameters;
using VAS.Devices.CardTerminals.Commands;
using System.Runtime.InteropServices;
using System.IO;

namespace VAS.Devices.CardTerminals
{
    [ComVisible(true)]
    [ProgId("CardTerminalLibrary.pay")]


    public class pay : Ipay
    {
        static string lastStatus;
        public string paymentCall(string value, string path)
        {
            lastStatus = string.Empty;
            string _configuration_G = @"
            <Config>
              <!--<Transport>Serial</Transport>-->
              <Transport>Network</Transport>
              <!--<TransportSettings>
                <Port>COM20</Port>
                <BaudRate>9600</BaudRate>
                <StopBits>One</StopBits>
              </TransportSettings>-->
              <TransportSettings>
                <RemoteIP>***************</RemoteIP>
                <RemotePort>22000</RemotePort>
              </TransportSettings>
              <RegistrationCommand>
                <ECRPrintsAdministrationReceipts>True</ECRPrintsAdministrationReceipts>
                <ECRPrintsPaymentReceipt>True</ECRPrintsPaymentReceipt>
                <PTDisableAmountInput>True</PTDisableAmountInput>
                <PTDisableAdministrationFunctions>True</PTDisableAdministrationFunctions>
              </RegistrationCommand>
            </Config>
            ";
            string _paymentSettings = @"<Payment><Amount>" + value + "</Amount></Payment>";
            ConfigFactory cf = new ConfigFactory();
            string _configuration = cf.initConfig(@path) ?? _configuration_G; 

            XmlDocument configuration = new XmlDocument();
            try
            {
                LogManager.Global = new LogManager(true, new TextLogger(null, LogLevel.Everything, "VAS", Console.Out));


                configuration.LoadXml(_configuration);
            }
            catch (Exception exp)
            {
                return _configuration + " nicht gefunden.";
            }
            XmlDocument paymentSettings = new XmlDocument();
            paymentSettings.LoadXml(_paymentSettings);

            ICommandEnvironment environment = new ZVTCommandEnvironment(configuration.DocumentElement);
            environment.StatusReceived += new IntermediateStatusDelegate(environment_StatusReceived);
            ClassifyCommandResult(environment.CreateInitialisationCommand(null).Execute());

            PaymentResult result = environment.CreatePaymentCommand(paymentSettings.DocumentElement).Execute();
            if (result.Success == false) { ClassifyCommandResult(result); return lastStatus; };
            XmlDocument authorisationIdentifier = new XmlDocument();
            authorisationIdentifier.AppendChild(authorisationIdentifier.CreateElement("Data"));
            result.Data.WriteXml(authorisationIdentifier.DocumentElement);
            using (var stringWriter = new StringWriter())
            using (var xmlTextWriter = XmlWriter.Create(stringWriter))
            {
                result.Data.ToXml().WriteTo(xmlTextWriter);
                xmlTextWriter.Flush();
                return lastStatus;
            }
            //ClassifyCommandResult(environment.CreateReversalCommand(authorisationIdentifier.DocumentElement).Execute());

            //ClassifyCommandResult(environment.CreateReportCommand(null).Execute());

        }


        static void environment_StatusReceived(IntermediateStatus status)
        {
            lastStatus = lastStatus + "\n" + status.ToString();
           // Console.WriteLine(status);
        }

        static void ClassifyCommandResult(CommandResult cmdResult)
        {
            if (cmdResult.Success == false) { }
                //lastStatus = "Zahlung fehlgeschlagen!";
        }
        #region IDisposable Members

        public void Dispose()
        {

        }

        #endregion
    }
}

