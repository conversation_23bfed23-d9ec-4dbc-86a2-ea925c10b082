using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;

namespace VAS.Devices.CardTerminals
{


    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "", IsNullable = false)]
    public class Config
    {
        public Config()
        {
            this.TransportSettings = new ConfigTransportSettings();
            this.registrationCommandField = new ConfigRegistrationCommand();
           
        }
        private string transportField;

        private ConfigTransportSettings transportSettingsField;
        private ConfigRegistrationCommand registrationCommandField;

        [UserScopedSetting()]
        [SettingsSerializeAs(System.Configuration.SettingsSerializeAs.Xml)]
        public string Transport
        {
            get
            {
                return this.transportField;
            }
            set
            {
                this.transportField = value;
            }
        }

        [UserScopedSetting()]
        [SettingsSerializeAs(System.Configuration.SettingsSerializeAs.Xml)]
        public ConfigTransportSettings TransportSettings
        {
            get
            {
                return this.transportSettingsField;
            }
            set
            {
                this.transportSettingsField = value;
            }
        }
        [UserScopedSetting()]
        [SettingsSerializeAs(System.Configuration.SettingsSerializeAs.Xml)]
        public ConfigRegistrationCommand RegistrationCommand
        {
            get
            {
                return this.registrationCommandField;
            }
            set
            {
                this.registrationCommandField = value;
            }
        }
    }

}


[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
public partial class ConfigTransportSettings
{

    private string portField;

    private ushort baudRateField;

    private string stopBitsField;

    private string remoteIPField;

    private ushort remotePortField;

    [UserScopedSetting()]
    [SettingsSerializeAs(System.Configuration.SettingsSerializeAs.Xml)]
    public string Port
    {
        get
        {
            return this.portField;
        }
        set
        {
            this.portField = value;
        }
    }

    [UserScopedSetting()]
    [SettingsSerializeAs(System.Configuration.SettingsSerializeAs.Xml)]
    public ushort BaudRate
    {
        get
        {
            return this.baudRateField;
        }
        set
        {
            this.baudRateField = value;
        }
    }

    [UserScopedSetting()]
    [SettingsSerializeAs(System.Configuration.SettingsSerializeAs.Xml)]
    public string StopBits
    {
        get
        {
            return this.stopBitsField;
        }
        set
        {
            this.stopBitsField = value;
        }
    }

    [UserScopedSetting()]
    [SettingsSerializeAs(System.Configuration.SettingsSerializeAs.Xml)]
    public string RemoteIP
    {
        get
        {
            return this.remoteIPField;
        }
        set
        {
            this.remoteIPField = value;
        }
    }

    [UserScopedSetting()]
    [SettingsSerializeAs(System.Configuration.SettingsSerializeAs.Xml)]
    public ushort RemotePort
    {
        get
        {
            return this.remotePortField;
        }
        set
        {
            this.remotePortField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true)]
public partial class ConfigRegistrationCommand
{

    private string eCRPrintsAdministrationReceiptsField;

    private string eCRPrintsPaymentReceiptField;

    private string pTDisableAmountInputField;

    private string pTDisableAdministrationFunctionsField;

    [UserScopedSetting()]
    [SettingsSerializeAs(System.Configuration.SettingsSerializeAs.Xml)]
    public string ECRPrintsAdministrationReceipts
    {
        get
        {
            return this.eCRPrintsAdministrationReceiptsField;
        }
        set
        {
            this.eCRPrintsAdministrationReceiptsField = value;
        }
    }

    [UserScopedSetting()]
    [SettingsSerializeAs(System.Configuration.SettingsSerializeAs.Xml)]
    public string ECRPrintsPaymentReceipt
    {
        get
        {
            return this.eCRPrintsPaymentReceiptField;
        }
        set
        {
            this.eCRPrintsPaymentReceiptField = value;
        }
    }

    [UserScopedSetting()]
    [SettingsSerializeAs(System.Configuration.SettingsSerializeAs.Xml)]
    public string PTDisableAmountInput
    {
        get
        {
            return this.pTDisableAmountInputField;
        }
        set
        {
            this.pTDisableAmountInputField = value;
        }
    }

    [UserScopedSetting()]
    [SettingsSerializeAs(System.Configuration.SettingsSerializeAs.Xml)]
    public string PTDisableAdministrationFunctions
    {
        get
        {
            return this.pTDisableAdministrationFunctionsField;
        }
        set
        {
            this.pTDisableAdministrationFunctionsField = value;
        }
    }
}




