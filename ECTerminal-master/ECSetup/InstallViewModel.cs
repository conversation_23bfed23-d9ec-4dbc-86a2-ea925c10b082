using NLog;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using Telerik.Windows.Controls;

namespace ECSetup
{
    public class InstallViewModel : ViewModelBase, INotifyPropertyChanged
    {
        private static Logger logger = LogManager.GetCurrentClassLogger();
        private List<string> InstallResources = new List<string>() { "CardTerminalLibrary.tlb", "ecterminalkey.snk", "CardTerminalConfig.xml", "CardTerminalLibrary.dll", "CardTerminalLibrary.pdb" };
        string target;
        private bool installed = false;
        private bool _busy;
        public bool Busy
        {
            get { return _busy; }
            set
            {
                if (value != _busy)
                {
                    _busy = value;
                    OnPropertyChanged("Busy");
                }
            }
        }
        private string _StatusText;
        public string StatusText
        {
            get { return _StatusText; }
            set
            {
                if (value != _StatusText)
                {
                    _StatusText = value;
                    OnPropertyChanged("StatusText");
                }
            }
        }
        public InstallViewModel()
        {
            LoadedCommand = new DelegateCommand(OnLoadedCommandExecuted);

        }

        private void OnLoadedCommandExecuted(object obj)
        {
            try
            {
                if (installed == false)
                {
                    StatusText = "Installation gestartet";
                    Busy = true;
                    string target = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + "\\wing71";
                    if (!Directory.Exists(target))
                    {
                        Directory.CreateDirectory(target);
                    }
                    foreach (var er in Assembly.GetExecutingAssembly().GetManifestResourceNames())
                    {
                        if (er.Contains("ECSetup"))
                        {
                            Application.Current.Dispatcher.BeginInvoke(
      DispatcherPriority.Background,
      new Action(() => StatusText = StatusText + Environment.NewLine + (er) + " wird kopiert."));

                        }
                    }

                    foreach (var item in InstallResources)
                    {

                        using (Stream input = Assembly.GetExecutingAssembly().GetManifestResourceStream("ECSetup.Data." + item))
                        using (Stream output = File.Create(target + "\\" + item))
                        {
                            CopyStream(input, output);
                        }
                    }
                    string command;
                    if (Directory.Exists(@"C:\WINDOWS\Microsoft.NET\Framework\v4.0.30319"))
                    {
                        command = @"C:\WINDOWS\Microsoft.NET\Framework\v4.0.30319\regasm /tlb:" + target + @"\CardTerminalLibrary.tlb " + target + @"\CardTerminalLibrary.dll";
                    }
                    else
                    {
                        var fileDialog = new System.Windows.Forms.OpenFileDialog();
                        var result = fileDialog.ShowDialog();
                        string file;
                        switch (result)
                        {
                            case System.Windows.Forms.DialogResult.OK:
                                file = fileDialog.FileName;
                                break;
                            case System.Windows.Forms.DialogResult.Cancel:
                            default:
                                throw new Exception("Regasm.exe zur Registrierung der DLL wurde nicht gefunden.");

                        }
                        if (File.Exists(file))
                        {
                            command = @file + @"/tlb:" + target + @"\CardTerminalLibrary.tlb " + target + @"\CardTerminalLibrary.dll";
                        }
                        else
                        {
                            throw new Exception("Regasm.exe zur Registrierung der DLL wurde nicht gefunden.");
                        }
                    }
                    Process.Start("cmd", "/K" + command + "\n pause");
                    /*  Assembly asm = Assembly.LoadFile(target + "\\CardTerminalLibrary.dll");

                                          RegistrationServices regAsm = new RegistrationServices();

                                          bool bResult = regAsm.RegisterAssembly(asm, AssemblyRegistrationFlags.SetCodeBase);
                                         */
                    Busy = false;
                    Application.Current.Dispatcher.BeginInvoke(
                      DispatcherPriority.Background,
      new Action(() => StatusText = StatusText + Environment.NewLine + "Dateien erfolgreich kopiert. DLL registriert. \n Bitte nehmen Sie die entsprechenden Änderungen an den Config vor. "));
                    installed = true;
                }

            }
            catch (Exception exp)
            {
                RadWindow.Alert("Fehler beim kopieren. Bitte Log beachten! \n " + exp.Message);
                logger.Fatal(exp, "Error bei Installation");
                Application.Current.Shutdown();
            }
        }


        public DelegateCommand LoadedCommand { get; private set; }
        public static void CopyStream(Stream input, Stream output)
        {
            byte[] buffer = new byte[8192];

            int bytesRead;
            while ((bytesRead = input.Read(buffer, 0, buffer.Length)) > 0)
            {
                output.Write(buffer, 0, bytesRead);
            }
        }
    }
}
