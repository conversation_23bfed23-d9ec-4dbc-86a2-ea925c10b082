using NLog;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Telerik.Windows.Controls;

namespace ECSetup
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private static Logger logger = LogManager.GetCurrentClassLogger();
        private List<string> InstallResources = new List<string>() { "CardTerminalLibrary.tlb", "ecterminalkey.snk", "CardTerminalConfig.xml", "CardTerminalLibrary.dll", "CardTerminalLibrary.pdb" };
        string target = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + "\\wing71";
        public MainWindow()
        {
            InitializeComponent();
            Wizard.Cancel += Wizard_Cancel;
            Wizard.Finish += Wizard_Finish;
        }

        private void Wizard_Finish(object sender, NavigationButtonsEventArgs e)
        {
            Process.Start("notepad.exe", target + "\\CardTerminalConfig.xml");
        }

        private void Wizard_Cancel(object sender, Telerik.Windows.Controls.NavigationButtonsEventArgs e)
        {
            Application.Current.Shutdown();
        }
    }
}
