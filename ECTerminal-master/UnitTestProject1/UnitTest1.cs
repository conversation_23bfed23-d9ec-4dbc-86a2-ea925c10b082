using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.IO.Pipes;
using System.Text;

namespace UnitTestProject1
{
    [TestClass]
    public class UnitTest1
    {
        [TestMethod]
        public void TestMethod1()
        {
           var _server = new NamedPipeServerStream(@"TestPipe", PipeDirection.Out, 1, PipeTransmissionMode.Message);
            _server.WaitForConnection();
            Console.WriteLine(_server.IsConnected);
            Console.WriteLine("Client connected\n Sending message");
            byte[] buff = Encoding.UTF8.GetBytes("Test message");
            _server.Write(buff, 0, buff.Length);
        }
    }
}
