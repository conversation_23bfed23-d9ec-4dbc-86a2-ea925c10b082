<Window x:Class="ECSetup.MainWindow"
                xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
        x:Name="Main"
        xmlns:local="clr-namespace:ECSetup"
        Title=" ECTerminal Installation Wizard" Height="350" Width="530">
    <Window.DataContext>
        <local:InstallViewModel></local:InstallViewModel>
    </Window.DataContext>
    <Grid>
        <telerik:RadBusyIndicator x:Name="Busy" IsBusy="{Binding Busy}">
            <telerik:RadWizard x:Name="Wizard"  >

                <telerik:RadWizard.WizardPages>

                    <telerik:WizardPage ButtonsVisibilityMode="Next,Cancel" CancelButtonContent="Beenden" AllowFinish="True">
                        <telerik:WizardPage.HeaderTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Label VerticalContentAlignment="Center">ECTerminal Installation Wizard v1.0</Label>
                                    <Image Source="img/vas.png" Height="55" HorizontalAlignment="Right" VerticalAlignment="Center" />
                                </StackPanel>
                            </DataTemplate>
                        </telerik:WizardPage.HeaderTemplate>
                        <TextBox IsReadOnly="True" TextWrapping="Wrap" TextAlignment="Justify" HorizontalContentAlignment="Left" VerticalContentAlignment="Top">
                            Willkommen zur ECTerminal-Installation. Bitte beachten Sie, dass nach der Installation die Config-Datei korrekt eingerichtet sein muss, damit das Gerät angesprochen werden kann. Außerdem muss Ihr EC-Terminal 
                        die ZVT-Schnittstelle unterstützen.
                            &#10;
                            &#xA;
                            Getestet an Verifon h5000.
                        </TextBox>
                    </telerik:WizardPage>
                    <telerik:WizardPage ButtonsVisibilityMode="Cancel,Finish,Next"  CancelButtonContent="Beenden" FinishButtonContent="Config bearbeiten" AllowFinish="True" x:Name="InstallPage" >
                        <telerik:EventToCommandBehavior.EventBindings>
                            <telerik:EventBinding Command="{Binding LoadedCommand}" EventName="GotFocus" CommandParameter="{Binding ElementName=InstallPage}"></telerik:EventBinding>
                        </telerik:EventToCommandBehavior.EventBindings>
                        <telerik:WizardPage.HeaderTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Label VerticalContentAlignment="Center">ECTerminal Installation Wizard v1.0</Label>
                                    <Image Source="img/vas.png" Height="55" HorizontalAlignment="Right" VerticalAlignment="Center" />
                                </StackPanel>
                            </DataTemplate>
                        </telerik:WizardPage.HeaderTemplate>
                        <TextBox IsReadOnly="True" x:Name="StatusBox" TextAlignment="Left" HorizontalContentAlignment="Left" VerticalContentAlignment="Top" Text="{Binding StatusText}">
                         
                        </TextBox>
                    </telerik:WizardPage>
                </telerik:RadWizard.WizardPages>
            </telerik:RadWizard>
        </telerik:RadBusyIndicator>
    </Grid>
</Window>
