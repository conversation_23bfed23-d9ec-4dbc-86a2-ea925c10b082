using System;
using System.Collections.Generic;
using System.IO.Pipes;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TestClientApp
{
    class Program
    {
        static void Main(string[] args)
        {
            var _server = new NamedPipeClientStream(@".", @"VPayPipe", PipeDirection.Out, PipeOptions.Asynchronous);
            _server.Connect(15000);
            Console.WriteLine(_server.IsConnected);
            Console.WriteLine("Client connected\n Sending message");
            byte[] buff = Encoding.UTF8.GetBytes("1234");
            _server.Write(buff, 0, buff.Length);
            _server.Dispose();
            Console.ReadKey();
        }
    }
}
