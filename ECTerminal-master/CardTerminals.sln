
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 15
VisualStudioVersion = 15.0.26430.15
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CardTerminalLibrary", "CardTerminalLibrary\CardTerminalLibrary.csproj", "{9E899822-A4D4-47D4-9DAA-9E8279F2201E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CardTerminals", "CardTerminals.Tests\CardTerminals.csproj", "{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ECSetup", "ECSetup\ECSetup.csproj", "{A649B50D-94B0-43B5-9FBF-98FF26EF1C69}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VASCard", "VASCard\VASCard.csproj", "{3E511447-D1F5-43B3-9772-B36193E3AE0A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UnitTestProject1", "UnitTestProject1\UnitTestProject1.csproj", "{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TestClientApp", "TestClientApp\TestClientApp.csproj", "{E9003654-C04B-4E6C-9B68-5396873D550F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|Mixed Platforms = Debug|Mixed Platforms
		Debug|x86 = Debug|x86
		MDebug|Any CPU = MDebug|Any CPU
		MDebug|Mixed Platforms = MDebug|Mixed Platforms
		MDebug|x86 = MDebug|x86
		Release|Any CPU = Release|Any CPU
		Release|Mixed Platforms = Release|Mixed Platforms
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.Debug|Any CPU.ActiveCfg = Debug|x86
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.Debug|Any CPU.Build.0 = Debug|x86
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.Debug|Mixed Platforms.ActiveCfg = Debug|x86
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.Debug|Mixed Platforms.Build.0 = Debug|x86
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.Debug|x86.ActiveCfg = Debug|x86
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.Debug|x86.Build.0 = Debug|x86
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.MDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.MDebug|Any CPU.Build.0 = Debug|Any CPU
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.MDebug|Mixed Platforms.ActiveCfg = Debug|x86
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.MDebug|Mixed Platforms.Build.0 = Debug|x86
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.MDebug|x86.ActiveCfg = Debug|x86
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.MDebug|x86.Build.0 = Debug|x86
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.Release|Any CPU.Build.0 = Release|Any CPU
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.Release|Mixed Platforms.ActiveCfg = Release|x86
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.Release|Mixed Platforms.Build.0 = Release|x86
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.Release|x86.ActiveCfg = Release|x86
		{9E899822-A4D4-47D4-9DAA-9E8279F2201E}.Release|x86.Build.0 = Release|x86
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.Debug|Mixed Platforms.ActiveCfg = Debug|x86
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.Debug|Mixed Platforms.Build.0 = Debug|x86
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.Debug|x86.ActiveCfg = Debug|x86
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.Debug|x86.Build.0 = Debug|x86
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.MDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.MDebug|Any CPU.Build.0 = Debug|Any CPU
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.MDebug|Mixed Platforms.ActiveCfg = Debug|x86
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.MDebug|Mixed Platforms.Build.0 = Debug|x86
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.MDebug|x86.ActiveCfg = Debug|x86
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.MDebug|x86.Build.0 = Debug|x86
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.Release|Any CPU.Build.0 = Release|Any CPU
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.Release|Mixed Platforms.ActiveCfg = Release|x86
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.Release|Mixed Platforms.Build.0 = Release|x86
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.Release|x86.ActiveCfg = Release|x86
		{2DF3420F-F597-4B98-AA20-3A0FC81F51D0}.Release|x86.Build.0 = Release|x86
		{A649B50D-94B0-43B5-9FBF-98FF26EF1C69}.Debug|Any CPU.ActiveCfg = Debug|x86
		{A649B50D-94B0-43B5-9FBF-98FF26EF1C69}.Debug|Mixed Platforms.ActiveCfg = Debug|x86
		{A649B50D-94B0-43B5-9FBF-98FF26EF1C69}.Debug|Mixed Platforms.Build.0 = Debug|x86
		{A649B50D-94B0-43B5-9FBF-98FF26EF1C69}.Debug|x86.ActiveCfg = Debug|x86
		{A649B50D-94B0-43B5-9FBF-98FF26EF1C69}.Debug|x86.Build.0 = Debug|x86
		{A649B50D-94B0-43B5-9FBF-98FF26EF1C69}.MDebug|Any CPU.ActiveCfg = Release|x86
		{A649B50D-94B0-43B5-9FBF-98FF26EF1C69}.MDebug|Any CPU.Build.0 = Release|x86
		{A649B50D-94B0-43B5-9FBF-98FF26EF1C69}.MDebug|Mixed Platforms.ActiveCfg = Debug|x86
		{A649B50D-94B0-43B5-9FBF-98FF26EF1C69}.MDebug|Mixed Platforms.Build.0 = Debug|x86
		{A649B50D-94B0-43B5-9FBF-98FF26EF1C69}.MDebug|x86.ActiveCfg = Debug|x86
		{A649B50D-94B0-43B5-9FBF-98FF26EF1C69}.MDebug|x86.Build.0 = Debug|x86
		{A649B50D-94B0-43B5-9FBF-98FF26EF1C69}.Release|Any CPU.ActiveCfg = Release|x86
		{A649B50D-94B0-43B5-9FBF-98FF26EF1C69}.Release|Mixed Platforms.ActiveCfg = Release|x86
		{A649B50D-94B0-43B5-9FBF-98FF26EF1C69}.Release|Mixed Platforms.Build.0 = Release|x86
		{A649B50D-94B0-43B5-9FBF-98FF26EF1C69}.Release|x86.ActiveCfg = Release|x86
		{A649B50D-94B0-43B5-9FBF-98FF26EF1C69}.Release|x86.Build.0 = Release|x86
		{3E511447-D1F5-43B3-9772-B36193E3AE0A}.Debug|Any CPU.ActiveCfg = Debug|x86
		{3E511447-D1F5-43B3-9772-B36193E3AE0A}.Debug|Any CPU.Build.0 = Debug|x86
		{3E511447-D1F5-43B3-9772-B36193E3AE0A}.Debug|Mixed Platforms.ActiveCfg = Debug|x86
		{3E511447-D1F5-43B3-9772-B36193E3AE0A}.Debug|Mixed Platforms.Build.0 = Debug|x86
		{3E511447-D1F5-43B3-9772-B36193E3AE0A}.Debug|x86.ActiveCfg = Debug|x86
		{3E511447-D1F5-43B3-9772-B36193E3AE0A}.Debug|x86.Build.0 = Debug|x86
		{3E511447-D1F5-43B3-9772-B36193E3AE0A}.MDebug|Any CPU.ActiveCfg = Release|x86
		{3E511447-D1F5-43B3-9772-B36193E3AE0A}.MDebug|Any CPU.Build.0 = Release|x86
		{3E511447-D1F5-43B3-9772-B36193E3AE0A}.MDebug|Mixed Platforms.ActiveCfg = Debug|x86
		{3E511447-D1F5-43B3-9772-B36193E3AE0A}.MDebug|Mixed Platforms.Build.0 = Debug|x86
		{3E511447-D1F5-43B3-9772-B36193E3AE0A}.MDebug|x86.ActiveCfg = Debug|x86
		{3E511447-D1F5-43B3-9772-B36193E3AE0A}.MDebug|x86.Build.0 = Debug|x86
		{3E511447-D1F5-43B3-9772-B36193E3AE0A}.Release|Any CPU.ActiveCfg = Release|x86
		{3E511447-D1F5-43B3-9772-B36193E3AE0A}.Release|Mixed Platforms.ActiveCfg = Release|x86
		{3E511447-D1F5-43B3-9772-B36193E3AE0A}.Release|Mixed Platforms.Build.0 = Release|x86
		{3E511447-D1F5-43B3-9772-B36193E3AE0A}.Release|x86.ActiveCfg = Release|x86
		{3E511447-D1F5-43B3-9772-B36193E3AE0A}.Release|x86.Build.0 = Release|x86
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.Debug|x86.Build.0 = Debug|Any CPU
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.MDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.MDebug|Any CPU.Build.0 = Debug|Any CPU
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.MDebug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.MDebug|Mixed Platforms.Build.0 = Debug|Any CPU
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.MDebug|x86.ActiveCfg = Debug|Any CPU
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.MDebug|x86.Build.0 = Debug|Any CPU
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.Release|Any CPU.Build.0 = Release|Any CPU
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.Release|x86.ActiveCfg = Release|Any CPU
		{F7A84805-3F28-4B78-8D1E-5ADFF6D96DD9}.Release|x86.Build.0 = Release|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.Debug|x86.Build.0 = Debug|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.MDebug|Any CPU.ActiveCfg = Debug|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.MDebug|Any CPU.Build.0 = Debug|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.MDebug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.MDebug|Mixed Platforms.Build.0 = Debug|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.MDebug|x86.ActiveCfg = Debug|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.MDebug|x86.Build.0 = Debug|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.Release|Any CPU.Build.0 = Release|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.Release|x86.ActiveCfg = Release|Any CPU
		{E9003654-C04B-4E6C-9B68-5396873D550F}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
