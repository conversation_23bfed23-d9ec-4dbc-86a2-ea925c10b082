using System;

namespace VAS.Devices.CardTerminals
{
    partial class HomeScreen
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.amountLabel = new System.Windows.Forms.Label();
            this.logBox = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // amountLabel
            // 
            this.amountLabel.Dock = System.Windows.Forms.DockStyle.Top;
            this.amountLabel.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F);
            this.amountLabel.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.amountLabel.Location = new System.Drawing.Point(0, 0);
            this.amountLabel.Margin = new System.Windows.Forms.Padding(10, 0, 3, 0);
            this.amountLabel.Name = "amountLabel";
            this.amountLabel.Size = new System.Drawing.Size(405, 61);
            this.amountLabel.TabIndex = 1;
            this.amountLabel.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.amountLabel.UseWaitCursor = true;
            this.amountLabel.Click += new System.EventHandler(this.amountLabel_Click);
            // 
            // logBox
            // 
            this.logBox.BackColor = System.Drawing.SystemColors.Menu;
            this.logBox.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.logBox.Cursor = System.Windows.Forms.Cursors.WaitCursor;
            this.logBox.Font = new System.Drawing.Font("Microsoft Sans Serif", 10F, System.Drawing.FontStyle.Bold);
            this.logBox.Location = new System.Drawing.Point(13, 64);
            this.logBox.Name = "logBox";
            this.logBox.Size = new System.Drawing.Size(380, 16);
            this.logBox.TabIndex = 2;
            this.logBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.logBox.UseWaitCursor = true;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.ForeColor = System.Drawing.SystemColors.AppWorkspace;
            this.label1.Location = new System.Drawing.Point(10, 85);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(391, 13);
            this.label1.TabIndex = 3;
            this.label1.Text = "Fenster mit Mausklick schließen. Zahlungsvorgang wird im Hintergrund forgesetzt.";
            // 
            // HomeScreen
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.SystemColors.Control;
            this.ClientSize = new System.Drawing.Size(405, 107);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.logBox);
            this.Controls.Add(this.amountLabel);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.Name = "HomeScreen";
            this.Text = "HomeScreen";
            this.UseWaitCursor = true;
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.HomeScreen_FormClosed);
            this.Load += new System.EventHandler(this.HomeScreen_Load);
            this.Shown += new System.EventHandler(this.HomeScreen_Shown);
            this.Click += new System.EventHandler(this.HomeScreen_Click);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        private void amountLabel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        private System.Windows.Forms.Label amountLabel;
        private System.Windows.Forms.TextBox logBox;
        private System.Windows.Forms.Label label1;
    }
}