using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;

namespace VAS.Devices.CardTerminals
{
    class ConfigFactory
    {
       private XmlDocument xmlConfig;
        string path;
        public ConfigFactory(string path)
        {
            this.path = Properties.Resources.Path;
            
        }

        public void initConfig()
        {

        //    var reader = new XmlTextReader(@path);
            var config = new XmlDocument();

            config.PreserveWhitespace = false;
            config.Load(@path);
           // reader = config.FirstChild;
            XmlNode node = config.FirstChild;

            foreach (XmlNode child in node.ChildNodes)
            {
                if (child.Name.Equals("Transport"))
                {
                    Properties.Settings.Default.Transport = child.InnerText;
                }
                else if (child.Name.Equals("TransportSettings"))
                {
                    foreach (XmlNode e in child.ChildNodes)
                    {

                        if (Properties.Settings.Default[e.Name] != null)
                        {
                            Properties.Settings.Default[e.Name] = e.InnerText;
                        }
                        else
                        {


                            System.Configuration.SettingsProperty prop = new System.Configuration.SettingsProperty(e.Name);
                            prop.Name = e.Name;
                            Properties.Settings.Default.Properties.Add(prop);
                            Properties.Settings.Default[e.Name] = e.InnerText;
                        }
                    }

                }
                else if (child.Name.Equals("RegistrationCommand"))
                {
                    foreach (XmlNode e in child.ChildNodes)
                    {
                      
                        if (Properties.Settings.Default[e.Name] != null)
                        {
                            Properties.Settings.Default[e.Name] = e.InnerText;
                        }
                        else
                        {


                            System.Configuration.SettingsProperty prop = new System.Configuration.SettingsProperty(e.Name);
                            prop.Name = e.Name;
                            Properties.Settings.Default.Properties.Add(prop);
                            Properties.Settings.Default[e.Name] = e.InnerText;
                        }
                    }
                }
            }
        }


        public string configString()
        {

            xmlConfig = new XmlDocument();
            XmlWriterSettings settings = new XmlWriterSettings();
            settings.OmitXmlDeclaration = true;
            using (XmlWriter writer = xmlConfig.CreateNavigator().AppendChild())
            {
                writer.WriteStartDocument();
                writer.WriteStartElement("Config");
                writer.WriteElementString("Transport", Properties.Settings.Default.Transport);

                    writer.WriteStartElement("TransportSettings");
                if (Properties.Settings.Default.Transport.Equals("Network"))
                {
                    writer.WriteElementString("RemoteIP", Properties.Settings.Default.RemoteIP ?? "***************");
                    writer.WriteElementString("RemotePort", Properties.Settings.Default.RemotePort ?? "22000");  
                }
                else
                {
                    writer.WriteElementString("Port", Properties.Settings.Default.Port.ToString() ?? "COM7");
                    writer.WriteElementString("BaudRate", Properties.Settings.Default.BaudRate.ToString() ?? "9600");
                    writer.WriteElementString("StopBits", Properties.Settings.Default.StopBits ?? "ONE");


                }
                
                writer.WriteEndElement();

                writer.WriteStartElement("RegistrationCommand");

                writer.WriteElementString("ECRPrintsAdministrationReceipts", Properties.Settings.Default.ECRPrintsAdministrationReceipts ?? "True");
                writer.WriteElementString("ECRPrintsPaymentReceipt", Properties.Settings.Default.ECRPrintsPaymentReceipt ?? "True");
                writer.WriteElementString("PTDisableAmountInput", Properties.Settings.Default.PTDisableAmountInput ?? "True");
                writer.WriteElementString("PTDisableAdministrationFunctions", Properties.Settings.Default.PTDisableAdministrationFunctions ?? "True");

                writer.WriteEndElement();
                writer.WriteEndElement();
                writer.WriteEndDocument();
            }
            return xmlConfig.OuterXml;

        }

        public void saveConfig(string altpath)
        {
            if (xmlConfig != null) { 
            
            xmlConfig.Save(altpath ?? path);
            }
            else
            {
                this.configString();
                xmlConfig.Save(altpath ?? path);
            }
        }
    }
}
