<?xml version="1.0" encoding="utf-8"?>
<configuration>
<configSections>
    <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
        <section name="VAS.Devices.CardTerminals.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
    </sectionGroup>
</configSections>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/></startup><userSettings>
        <VAS.Devices.CardTerminals.Properties.Settings>
            <setting name="Transport" serializeAs="String">
                <value />
            </setting>
            <setting name="Port" serializeAs="String">
                <value />
            </setting>
            <setting name="BaudRate" serializeAs="String">
                <value />
            </setting>
            <setting name="StopBits" serializeAs="String">
                <value />
            </setting>
            <setting name="RemoteIP" serializeAs="String">
                <value />
            </setting>
            <setting name="RemotePort" serializeAs="String">
                <value />
            </setting>
            <setting name="ECRPrintsAdministrationReceipts" serializeAs="String">
                <value />
            </setting>
            <setting name="ECRPrintsPaymentReceipt" serializeAs="String">
                <value />
            </setting>
            <setting name="PTDisableAmountInput" serializeAs="String">
                <value />
            </setting>
            <setting name="PTDisableAdministrationFunctions" serializeAs="String">
                <value />
            </setting>
        </VAS.Devices.CardTerminals.Properties.Settings>
    </userSettings>
</configuration>
