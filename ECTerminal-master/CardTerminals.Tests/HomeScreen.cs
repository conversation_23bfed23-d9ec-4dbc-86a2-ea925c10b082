using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using VAS.Devices.CardTerminals.Protocols.ZVT.TransportLayer;
using VAS.Diagnostic.Log;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer;
using System.IO.Ports;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer.Commands;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer.Parameters;
using VAS.Devices.CardTerminals.Commands;
using System.Xml.Linq;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Net;
using System.Collections;
using System.Web;

namespace VAS.Devices.CardTerminals
{
    public partial class HomeScreen : Form
    {
        private HttpListener Listener;
        private NotifyIcon trayIcon;
       
        public HomeScreen()
        {
           
            InitializeComponent();
            ConfigFactory cF = new ConfigFactory(@"CardTerminalConfig.xml");
            cF.initConfig();
            cF.configString();
            cF.saveConfig(@"CardTerminalConfig2.xml");
            // Visible = false;
            /*this.TransparencyKey = Color.Turquoise;
              this.BackColor = Color.Turquoise;*/
        }

        public static string GetRequestPostData(HttpListenerRequest request)
        {
            if (!request.HasEntityBody)
            {
                return null;
            }
            using (System.IO.Stream body = request.InputStream) // here we have data
            {
                using (System.IO.StreamReader reader = new System.IO.StreamReader(body, request.ContentEncoding))
                {
                    return reader.ReadToEnd();
                }
            }
        }



    
        void Exit(object sender, EventArgs e)
        {
            // Hide tray icon, otherwise it will remain shown until user mouses over it
            trayIcon.Visible = false;

            Application.Exit();
        }

        private void HomeScreen_Load(object sender, EventArgs e)
        {

           
            TemCom tc = new TemCom(this, this.amountLabel, this.logBox);
            
            trayIcon = new NotifyIcon()
            {
                Icon = new Icon(Properties.Resources.payment_card, 40, 40),
                ContextMenu = new ContextMenu(new MenuItem[] {
                
                new MenuItem("Zahlung", Payment),
                new MenuItem("Beenden", Exit),
                new MenuItem("Info", Info)
            }),
                Visible = true
            };

            ShowInTaskbar = false;
            this.FormBorderStyle = FormBorderStyle.None;
            this.CenterToScreen();
            this.BringToFront();
            this.Hide();
        }

        private void Info(object sender, EventArgs e)
        {
            InfoScreen iS = new InfoScreen();
            Thread thread = new Thread(() =>
            iS.ShowDialog());
            thread.IsBackground = false; //Background : will be terminated when you close your application
            thread.Start();
        }

        private void Payment(object sender, EventArgs e)
        {
            singlePayment sP = new singlePayment(trayIcon, this);
            Thread thread = new Thread(() => 
            sP.ShowDialog());
            thread.IsBackground = false; //Background : will be terminated when you close your application
            thread.Start(); 
        }

        private void HomeScreen_Shown(object sender, EventArgs e)
        {

          
        }


        private void HomeScreen_FormClosed(object sender, FormClosedEventArgs e)
        {
        }

        private void HomeScreen_Click(object sender, EventArgs e)
        {
          

	    this.WindowState = FormWindowState.Minimized;
        }
    }
}
