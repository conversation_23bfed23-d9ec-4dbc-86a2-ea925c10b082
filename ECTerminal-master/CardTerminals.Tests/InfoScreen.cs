using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace VAS.Devices.CardTerminals
{
    public partial class InfoScreen : Form
    {
        ConfigFactory cF = null;
        public InfoScreen()
        {
            this.FormBorderStyle = FormBorderStyle.None;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterScreen;
        
            this.Icon = new Icon(Properties.Resources.payment_card, 40, 40);
            cF = new ConfigFactory(@Properties.Resources.Path);
            InitializeComponent();



        }

        private void Info_Load(object sender, EventArgs e)
        {
            this.transportBox.SelectedText = Properties.Settings.Default.Transport;

            switch (Properties.Settings.Default.Transport)
            {
                case "Network":
                 
                    transportSettingsPanel.Controls.Add(textboxFactory("RemotePort",0 ,0));
                    transportSettingsPanel.Controls.Add(textboxFactory("RemoteIP", 0, transportSettingsPanel.Controls[transportSettingsPanel.Controls.Count-1].Location.Y+ transportSettingsPanel.Controls[transportSettingsPanel.Controls.Count - 1].Size.Height+10));
                    break;
                case "Seriel":
                    transportSettingsPanel.Controls.Add(textboxFactory("Port",0,0));
                    transportSettingsPanel.Controls.Add(textboxFactory("BaudRate",0, transportSettingsPanel.Controls[transportSettingsPanel.Controls.Count - 1].Location.Y + transportSettingsPanel.Controls[transportSettingsPanel.Controls.Count - 1].Size.Height + 10));
                    transportSettingsPanel.Controls.Add(textboxFactory("StopBits",0, transportSettingsPanel.Controls[transportSettingsPanel.Controls.Count - 1].Location.Y + transportSettingsPanel.Controls[transportSettingsPanel.Controls.Count - 1].Size.Height + 10));


                    break;
            }
        }

        void OnTextChanged(object sender, EventArgs e)
        {
            var textbox = (TextBox)sender;

            if (string.IsNullOrEmpty(textbox.Text))
            {
                textbox.Text = (string)textbox.Tag;

            }
        }

        void onBoxLeft(object sender, EventArgs e)
        {
            var textbox = (TextBox)sender;
            Properties.Settings.Default[textbox.Name] = textbox.Text;
            cF.saveConfig(null);
        }

        private Panel textboxFactory(string name, int x, int y)
        {
            Label lB = new Label()
            {
                Location = new Point(0, 0)
            };
            lB.Text = name+":";

            TextBox tB = new TextBox()
            {
                Name = name,
                Location = new Point(100, 0)
            };
            lB.Size = new Size(100,tB.Size.Height);
            lB.TextAlign = ContentAlignment.MiddleCenter;
            tB.Text = Properties.Settings.Default[name].ToString() ?? name;
            tB.TextChanged += new EventHandler(OnTextChanged);
            tB.LostFocus += new EventHandler(onBoxLeft);

            Panel tempPanel = new Panel() {
                Location = new Point(x, y),
            Size = new Size(100+tB.Size.Width,tB.Size.Height)
            };
           
            tempPanel.Controls.Add(lB);
            tempPanel.Controls.Add(tB);
            return tempPanel;
        }

   

        private void transportBox_SelectedValueChanged(object sender, EventArgs e)
        {
          
        }

        private void transportBox_TextChanged(object sender, EventArgs e)
        {
            Properties.Settings.Default.Transport = this.transportBox.Text.ToString();
            transportSettingsPanel.Controls.Clear();
            switch (Properties.Settings.Default.Transport)
            {
                case "Network":
                    transportSettingsPanel.Controls.Add(textboxFactory("RemotePort", 0, 0));
                    transportSettingsPanel.Controls.Add(textboxFactory("RemoteIP", 0, transportSettingsPanel.Controls[transportSettingsPanel.Controls.Count - 1].Location.Y + transportSettingsPanel.Controls[transportSettingsPanel.Controls.Count - 1].Size.Height + 10));
                    break;
                case "Seriel":
                    transportSettingsPanel.Controls.Add(textboxFactory("Port", 0, 0));
                    transportSettingsPanel.Controls.Add(textboxFactory("BaudRate", 0, 30));
                    transportSettingsPanel.Controls.Add(textboxFactory("StopBits", 0, 60));


                    break;
            }
            this.Refresh();
            transportSettingsPanel.Refresh();
        }
    }
  
}
