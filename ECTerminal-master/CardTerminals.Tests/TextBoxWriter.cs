using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using VAS.Devices.CardTerminals.Commands;

namespace VAS.Devices.CardTerminals
{
    public class TextBoxWriter :  TextWriter
    {
        private string tempRes = "";
        TextBox _output = null;
        StreamWriter _writer = null;
        public TextBoxWriter(TextBox output, StreamWriter writer)
        {
            _output = output;
            _writer = writer;
            
        }

        public override void Write(char value)
        {
            if (value.ToString().Equals("["))
                {
                value = '\0';
            }
            base.Write(value);
            if (value.ToString().StartsWith("["))
            {
                tempRes = "[";
            }
            else
            {
                if (tempRes.StartsWith("["))
                {
                    tempRes = tempRes + value.ToString();
                }
                if (value.ToString().StartsWith("\n")) {
                   _output.Invoke((MethodInvoker)delegate
                    {
                        _output.Text = tempRes;
                        _output.Refresh();
                    });
              
                tempRes = "";
                }
            }
            _writer.Write(value.ToString());
        }

        public override Encoding Encoding
        {
            get { return System.Text.Encoding.ASCII; }
        }
        


    }
}
