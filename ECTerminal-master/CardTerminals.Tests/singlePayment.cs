using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Windows.Forms;

namespace VAS.Devices.CardTerminals
{
    public partial class singlePayment : Form
    {
        private NotifyIcon nf;
        private HomeScreen homeScreen;

        public singlePayment(NotifyIcon notifyIcon, HomeScreen homeScreen)
        {
            this.FormBorderStyle = FormBorderStyle.None;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.ControlBox = false;
            this.homeScreen = homeScreen;
            nf = notifyIcon;
            this.Icon = new Icon(Properties.Resources.payment_card, 40, 40);
            InitializeComponent();

        }


        private void textBox1_KeyPress(object sender, KeyPressEventArgs e)
        {
            // allows 0-9, backspace, and decimal
            if (((e.<PERSON><PERSON><PERSON> < 48 || e.<PERSON><PERSON>har > 57) && e.KeyChar != 8 && e.KeyChar != 44))
            {
                e.Handled = true;
                return;
            }

            // checks to make sure only 1 decimal is allowed
            if (e.KeyChar == 46)
            {
                if ((sender as TextBox).Text.IndexOf(e.KeyChar) != -1)
                    e.Handled = true;
            }
        }

        private void pay_Click(object sender, EventArgs e)
        {
            string finCosts = "0";
            string[] costs = textBox1.Text.Split(',');
            if (costs.Length == 2)
            {
                finCosts = costs[0] + costs[1];
            }
            else if (costs.Length == 1)
            {
                finCosts = costs[0] + "00";
            }
            this.Hide();
            string url = "http://localhost:9090/ec=" + finCosts;

            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);

            HttpWebResponse response = (HttpWebResponse)request.GetResponse();

            Stream resStream = response.GetResponseStream();
            StreamReader rs = new StreamReader(resStream);
            /*   homeScreen.Invoke((MethodInvoker)delegate
               {

                   nf.BalloonTipText = "The quick brown fox. Jump!";
                   nf.BalloonTipIcon = ToolTipIcon.Info;
                   nf.BalloonTipTitle = "Alert!";
                   nf.ShowBalloonTip(500);
               });*/
        }

        private void textBox1_KeyDown(object sender, KeyEventArgs e)
        {

            if (e.KeyCode == Keys.Enter)
            {
                this.pay.PerformClick();

            }
        }

        private void singlePayment_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                exit.PerformClick();
            }
        }

        private void exit_Click(object sender, EventArgs e)
        {

            this.Close();

        }

        private void singlePayment_Load(object sender, EventArgs e)
        {

        }
    }
}
