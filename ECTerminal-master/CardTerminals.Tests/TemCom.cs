using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using VAS.Devices.CardTerminals.Protocols.ZVT.TransportLayer;
using VAS.Diagnostic.Log;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer;
using System.IO.Ports;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer.Commands;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer.Parameters;
using System.Windows.Forms;
using VAS.Devices.CardTerminals.Commands;
using System.Collections;
using System.Web;
using System.Xml;
using System.Xml.Linq;

namespace VAS.Devices.CardTerminals
{
    class TemCom
    {

        public static string price = "0";
        public static string _paymentSettings = null;
        private ICommandEnvironment environment;
        public static string _configuration = null;
        private HttpListener Listener;
        public static bool status;
        private Thread thread;
        private HomeScreen homeScreen;
        private bool RunningPayment;
        private Label amountLabel;
        private TextBox logbox;

        public TemCom(HomeScreen homeScreen, Label amountLabel, TextBox tb)
        {
            RunningPayment = false;
            this.logbox = tb;
            this.homeScreen = homeScreen;
            this.amountLabel = amountLabel;
            Listener = new HttpListener();
            Listener.Prefixes.Add("http://+:9090/");
            Listener.Start();
            Listener.BeginGetContext(new AsyncCallback(this.ListenerCallback), Listener);
            status = false;
        }


        static void environment_StatusReceived(IntermediateStatus status)
        {

            Console.WriteLine(status);
        }

        private void ListenerCallback(IAsyncResult Result)
        {

            HttpListenerContext Context;

            try
            {

                Context = Listener.EndGetContext(Result);

                // RequestInput = Context.Request.ToString();

                this.ProcessRequest(Context);

            }
            catch (HttpListenerException eR)
            {

                System.Diagnostics.Debug.WriteLine(eR.ToString());

            }
            try
            {

                Listener.BeginGetContext(new AsyncCallback(ListenerCallback), Listener);

            }
            catch (InvalidOperationException eR)
            {

                System.Diagnostics.Debug.WriteLine(eR.ToString());

            }

        }

        public void ContextResponse(HttpListenerContext context, string response)
        {
            byte[] bytes = System.Text.UTF8Encoding.Default.GetBytes(response);
            BitArray bits = new System.Collections.BitArray(bytes);
            context.Response.OutputStream.Write(bytes, 0, bytes.Length);
        }

        private void ProcessRequest(HttpListenerContext context)
        {
            if (RunningPayment == true)
            {
                ContextResponse(context, "Gerät ist beschäfigt.");
            }
            else
            {
                //RunningPayment = true;
                HttpListenerRequest Request = context.Request;
                string[] words = Request.Url.ToString().Split('/');

                if (words.Length >= 4)
                {
                    var parsed = HttpUtility.ParseQueryString(words[3]);
                    string costs = parsed["ec"];

                    if (costs != null)
                    {


                        try
                        {


                            homeScreen.Invoke((MethodInvoker)delegate
                             {
                                 homeScreen.Visible = true;
                                 homeScreen.WindowState = FormWindowState.Normal;
                                
                                 homeScreen.Focus();
                                 homeScreen.Activate();
                                 amountLabel.Text = "Zahlung von " + costs.Remove(costs.Length - 2) + "," + costs.Substring(costs.Length - 2) + " €";
                                 amountLabel.Refresh();
                             });
                            PaymentCall(costs, context);
                            /*  thread = new Thread(() => PaymentCall(costs));
                              thread.IsBackground = true; //Background : will be terminated when you close your application
                              thread.Start();*/


                        }
                        catch (Exception ex)
                        {
                            //Console.Write(ex.Message);
                            ContextResponse(context, "Gerät ist beschäftigt");
                            status = false;
                            // logBox.Text = "Unbekannter Fehler. Gerät ist beschäftigt.";
                        }
                    }
                    else
                    {
                        byte[] bytes = System.Text.UTF8Encoding.Default.GetBytes("Fehler beim Übermitteln des Befehls.");
                        BitArray bits = new System.Collections.BitArray(bytes);
                        context.Response.OutputStream.Write(bytes, 0, bytes.Length);
                    }

                }
                else
                {
                    ContextResponse(context, "Fehler beim Übermitteln des Befehls.");

                }
            }
            context.Response.Close();
             homeScreen.Invoke((MethodInvoker)delegate
             {

                 homeScreen.Visible = false;
                 homeScreen.WindowState = FormWindowState.Minimized;
                 amountLabel.Text = "Keine Zahlung anstehend";
                 amountLabel.Refresh();
             });
        }


        private void PaymentCall(string costs, HttpListenerContext _context)
        {

            StreamWriter writer = null;
            try
            {
                string exePath = Application.StartupPath;
                var ostrm = new FileStream(exePath + @"/" + costs + @".txt", FileMode.Create, FileAccess.Write);
                writer = new StreamWriter(ostrm);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Datei nicht gefunden");
                Console.WriteLine(ex.Message);

            }
            if (writer != null)
            {
                Console.SetOut(writer);
                Console.SetOut(new TextBoxWriter(logbox, writer));
            }
            string[] args = Environment.GetCommandLineArgs();
            var path = XDocument.Load(@"CardTerminalConfig.xml");
            if (path != null)
            {
                _configuration = path.ToString();
            }
            else
            {
                ContextResponse(_context, "Fehler");
                // Environment.Exit(1);
            }


            price = costs;

            _paymentSettings = String.Format("<Payment>\n<Amount>{0}</Amount></Payment>", price);
            TextLogger tl = new TextLogger(null, LogLevel.Verbose, "Terminal", writer);
            LogManager.Global = new LogManager(true, tl);

            XmlDocument configuration = new XmlDocument();
            configuration.LoadXml(_configuration);

            XmlDocument paymentSettings = new XmlDocument();
            paymentSettings.LoadXml(_paymentSettings);
            try
            {

                environment = new ZVTCommandEnvironment(configuration.DocumentElement);
                environment.StatusReceived += new IntermediateStatusDelegate(environment_StatusReceived);
                if (!ClassifyCommandResult(environment.CreateInitialisationCommand(null).Execute())) { ContextResponse(_context, "Error"); return; }

                PaymentResult result = environment.CreatePaymentCommand(paymentSettings.DocumentElement).Execute();
                if (!ClassifyCommandResult(result))
                {
                    ContextResponse(_context, "Error"); return;
                };

                XmlDocument authorisationIdentifier = new XmlDocument();
                authorisationIdentifier.AppendChild(authorisationIdentifier.CreateElement("Data"));
                result.Data.WriteXml(authorisationIdentifier.DocumentElement);

                ContextResponse(_context, "Ok");
            }
            catch (ArgumentException exp)
            {

                Console.WriteLine(exp.Message);

            }

            // Environment.Exit(0);


        }


        static bool ClassifyCommandResult(CommandResult cmdResult)
        {
            /*  Console.WriteLine(cmdResult.PrintDocuments);
            if (cmdResult.Success == false)
            {
                // throw new ArgumentException("Command not successful");
                return false;
              
                // Environment.Exit(1);
            }
            else
            {
                status = true;
            }*/
            return cmdResult.Success;
        }

    }
}
