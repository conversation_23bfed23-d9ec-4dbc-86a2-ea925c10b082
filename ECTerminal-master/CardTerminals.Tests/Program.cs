using System;
using System.Collections.Generic;
using System.Text;
using System.Xml;
using VAS.Devices.CardTerminals.Protocols.ZVT.TransportLayer;
using VAS.Diagnostic.Log;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer;
using System.IO.Ports;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer.Commands;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer.Parameters;
using VAS.Devices.CardTerminals.Commands;
using System.Xml.Linq;
using System.Windows.Forms;
namespace VAS.Devices.CardTerminals
{
    class Program
    {
 
        static void Main(string[] args)
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new HomeScreen());
        }
    }
}
