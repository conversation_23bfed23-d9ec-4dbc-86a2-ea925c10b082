<?xml version="1.0" encoding="UTF-8"?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="VASCard.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="TerminalConfig" Type="VAS.Devices.CardTerminals.Config" Scope="Application">
      <Value Profile="(Default)">&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt; &lt;Config xmlns:xsi=&quot;http://www.w3.org/2001/XMLSchema-instance&quot; xmlns:xsd=&quot;http://www.w3.org/2001/XMLSchema&quot;&gt;   &lt;Transport&gt;Network&lt;/Transport&gt;   &lt;TransportSettings&gt;     &lt;Port&gt;COM20&lt;/Port&gt;     &lt;BaudRate&gt;9600&lt;/BaudRate&gt;     &lt;StopBits&gt;One&lt;/StopBits&gt;     &lt;RemoteIP&gt;*************&lt;/RemoteIP&gt;     &lt;RemotePort&gt;22000&lt;/RemotePort&gt;   &lt;/TransportSettings&gt;   &lt;RegistrationCommand /&gt; &lt;/Config&gt;</Value>
    </Setting>
  </Settings>
</SettingsFile>

