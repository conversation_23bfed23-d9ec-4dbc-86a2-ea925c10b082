using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Pipes;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows;
using System.Xml;
using System.Xml.Serialization;
using Telerik.Windows.Controls;
using VAS.Devices.CardTerminals;
using VAS.Devices.CardTerminals.Commands;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer;
using VAS.Diagnostic.Log;

namespace VASCard
{
    public static class PipeClass
    {
        static PipeClass()
        {
            ApprovedStatus = new List<int> { 28, 160, 255 };
        }
        private static List<int> ApprovedStatus { get; set; }
        static string lastStatus;
        public static void InitPipe()
        {
            /*  NamedPipeServerStream pipeServer = new NamedPipeServerStream("VPayPipe", PipeDirection.InOut, 1, PipeTransmissionMode.Message, PipeOptions.Asynchronous);

              pipeServer.BeginWaitForConnection(callback: PipeConnected, state: pipeServer);
              */
        }

        private static void PipeConnected(IAsyncResult ar)
        {
            using (var conn = (NamedPipeServerStream) ar.AsyncState)
            {
                if (conn != null)
                {
                    try
                    {

                        conn.EndWaitForConnection(ar);
                        StringBuilder messageBuilder = new StringBuilder();
                        string messageChunk = string.Empty;
                        byte[] messageBuffer = new byte[1024];
                        do
                        {
                            conn.Read(messageBuffer, 0, messageBuffer.Length);
                            messageChunk = Encoding.UTF8.GetString(messageBuffer);
                            messageBuilder.Append(messageChunk);
                            messageBuffer = new byte[messageBuffer.Length];
                        }
                        while (!conn.IsMessageComplete);
                        var result = new String(messageBuilder.ToString().Where(Char.IsDigit).ToArray());
                        var payResult = "Fehler beim Übermitteln des Betrags (StatusCode: 13)";
                        if (result.All(Char.IsNumber))
                        {
                            Messenger.Default.Send<string>(result, "NewAmount");
                            Messenger.Default.Send<string>("EC Zahlung " + String.Format("{0:0.00}", Double.Parse(result) / 100) + " €", "Payload");
                            payResult = paymentCall(result, @Properties.Settings.Default.TerminalConfig);
                        }

                        var bytesArray = Encoding.UTF8.GetBytes(payResult);
                        conn.Write(bytesArray, 0, bytesArray.Length);




                    }
                    catch (IOException e)
                    {
                        Console.WriteLine("ERROR: {0}", e.Message);
                    }
                    finally
                    {
                        conn.Close();

                        Messenger.Default.Send<string>("", "PaymentDone");
                        InitPipe();
                    }
                }
            }
        }

        public static string paymentCall(string value, Config path)
        {
            XmlSerializer config = new XmlSerializer(typeof(Config));

            var xml = "";

            using (var sww = new StringWriter())
            {
                using (XmlWriter writer = XmlWriter.Create(sww))
                {
                    config.Serialize(writer, path);
                    xml = sww.ToString();
                }
            }
            lastStatus = string.Empty;
            string _configuration_G = @"
            <Config>
              <!--<Transport>Serial</Transport>-->
              <Transport>Network</Transport>
              <!--<TransportSettings>
                <Port>COM20</Port>
                <BaudRate>9600</BaudRate>
                <StopBits>One</StopBits>
              </TransportSettings>-->
              <TransportSettings>
                <RemoteIP>***************</RemoteIP>
                <RemotePort>22000</RemotePort>
              </TransportSettings>
             
            </Config>
            ";
            string _paymentSettings = @"<Payment><Amount>" + value + "</Amount></Payment>";
            ConfigFactory cf = new ConfigFactory();
            string _configuration = xml ?? _configuration_G;

            XmlDocument configuration = new XmlDocument();
            try
            {
                LogManager.Global = new LogManager(true, new TextLogger(null, LogLevel.Everything, "VAS", Console.Out));


                configuration.LoadXml(_configuration);
            }
#pragma warning disable CS0168 // Die Variable "exp" ist deklariert, wird aber nie verwendet.
            catch (Exception exp)
#pragma warning restore CS0168 // Die Variable "exp" ist deklariert, wird aber nie verwendet.
            {
                return _configuration + " nicht gefunden.";
            }
            XmlDocument paymentSettings = new XmlDocument();
            paymentSettings.LoadXml(_paymentSettings);

            ICommandEnvironment environment = new ZVTCommandEnvironment(configuration.DocumentElement);
            environment.StatusReceived += new IntermediateStatusDelegate(environment_StatusReceived);
            ClassifyCommandResult(environment.CreateInitialisationCommand(null).Execute());

            PaymentResult result = environment.CreatePaymentCommand(paymentSettings.DocumentElement).Execute();

            if (result.Success == false) { ClassifyCommandResult(result); return lastStatus; };
            XmlDocument authorisationIdentifier = new XmlDocument();
            authorisationIdentifier.AppendChild(authorisationIdentifier.CreateElement("Data"));
            result.Data.WriteXml(authorisationIdentifier.DocumentElement);
            using (var stringWriter = new StringWriter())
            using (var xmlTextWriter = XmlWriter.Create(stringWriter))
            {
                result.Data.ToXml().WriteTo(xmlTextWriter);
                xmlTextWriter.Flush();
                return lastStatus;
            }

        }

        public static void createClosing()
        {
            XmlSerializer config = new XmlSerializer(typeof(Config));

            var xml = "";

            using (var sww = new StringWriter())
            {
                using (XmlWriter writer = XmlWriter.Create(sww))
                {
                    config.Serialize(writer, Properties.Settings.Default.TerminalConfig);
                    xml = sww.ToString();
                }
            }
            XmlDocument configuration = new XmlDocument();
            try
            {
                LogManager.Global = new LogManager(true, new TextLogger(null, LogLevel.Everything, "VAS", Console.Out));


                configuration.LoadXml(xml);
            }
#pragma warning disable CS0168 // Die Variable "exp" ist deklariert, wird aber nie verwendet.
            catch (Exception exp)
#pragma warning restore CS0168 // Die Variable "exp" ist deklariert, wird aber nie verwendet.
            {

            }
            ICommandEnvironment environment = new ZVTCommandEnvironment(configuration.DocumentElement);
            var day = environment.CreateEndOfDayCommand(configuration.DocumentElement).Execute();
            var doc = day.PrintDocuments;
            foreach (var item in doc)
            {
                foreach (var item2 in item.PrintLines)
                {
                    foreach (var item3 in item2.Commands)
                    {
                        byte[] bytes = Encoding.Default.GetBytes(item3.Text.ToString());
                        var output = Encoding.ASCII.GetString(bytes);
                        Messenger.Default.Send<string>(output, "Payload");
                    }
                }
            }
        }

        public static void createClosing2()
        {
            XmlSerializer config = new XmlSerializer(typeof(Config));

            var xml = "";

            using (var sww = new StringWriter())
            {
                using (XmlWriter writer = XmlWriter.Create(sww))
                {
                    config.Serialize(writer, Properties.Settings.Default.TerminalConfig);
                    xml = sww.ToString();
                }
            }
            XmlDocument configuration = new XmlDocument();
            try
            {
                LogManager.Global = new LogManager(true, new TextLogger(null, LogLevel.Everything, "VAS", Console.Out));


                configuration.LoadXml(xml);
            }
#pragma warning disable CS0168 // Die Variable "exp" ist deklariert, wird aber nie verwendet.
            catch (Exception exp)
#pragma warning restore CS0168 // Die Variable "exp" ist deklariert, wird aber nie verwendet.
            {

            }
            ICommandEnvironment environment = new ZVTCommandEnvironment(configuration.DocumentElement);
            var day = environment.CreateResetCommand(configuration.DocumentElement).Execute();
        }



        static void environment_StatusReceived(IntermediateStatus status)
        {

            if (lastStatus != status.ToString())
            {

                Messenger.Default.Send<string>(status.ToString(), "Payload");
                Application.Current.Dispatcher.Invoke(System.Windows.Threading.DispatcherPriority.Background, new System.Threading.ThreadStart(delegate
            {

                Messenger.Default.Send<RadDesktopAlert>(new RadDesktopAlert() { Content = status.ToString(), Header = "EC Terminal Info", ShowDuration = 10000 }, "Alert");
            }));
            }
            if (ApprovedStatus.Contains(status.StatusCode))
            {
                lastStatus = status.ToString();
            }
         
        }

        static void ClassifyCommandResult(CommandResult cmdResult)
        {
            if (cmdResult.Success == false) { }
            // lastStatus = "13";
        }
    }
}
