<telerik:RadRibbonWindow x:Class="VASCard.MainWindow"
                xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
                            xmlns:Telerik_Windows_Controls_RibbonView_KeyTips="clr-namespace:Telerik.Windows.Controls.RibbonView.KeyTips;assembly=Telerik.Windows.Controls.RibbonView"
                          UseLayoutRounding="True"
                         xmlns:Local="clr-namespace:VASCard"    Icon="img\ec4.ico"
                        Closing="RadRibbonWindow_Closing" Title="EC Terminal" Height="450" Width="600" ResizeMode="CanMinimize" Loaded="RadRibbonWindow_Loaded" WindowStartupLocation="CenterScreen">
    <telerik:RadRibbonWindow.DataContext>
        <Local:VASCardModel/>
    </telerik:RadRibbonWindow.DataContext>
    <Window.Resources>
        <telerik:IconSources x:Key="IconPaths" LightBasePath="/Telerik.Windows.Controls.Spreadsheet;component/Images/Light/"
                                               DarkBasePath="/Telerik.Windows.Controls.Spreadsheet;component/Images/Dark/"/>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="8*"/>
            <RowDefinition Height="10*"/>
        </Grid.RowDefinitions>
        <telerik:RadRibbonView Grid.Row="0" DataContext="{Binding}"
                               HeaderBackground="Transparent"
                               ApplicationName="VAS EC-Terminal"
                               BackstageClippingElement="{Binding ElementName=LayoutRoot}" >
            <telerik:RadRibbonView.Items>
                <telerik:RadRibbonTab Header="Protokoll">
                    <telerik:RadRibbonGroup Header="Funktionen">
                        <telerik:RadRibbonButton Text="Tagesabschluss" x:Name="ClosingButton" Click="ClosingButton_Click" />

                        <telerik:RadRibbonButton Text="EC-Gerät neustart" x:Name="RestartButton" Click="RestartButton_Click" />
                    </telerik:RadRibbonGroup>
                </telerik:RadRibbonTab>
            </telerik:RadRibbonView.Items>
            <telerik:RadRibbonView.ApplicationButtonContent>
                <TextBlock Text="Start" Foreground="White"/>
            </telerik:RadRibbonView.ApplicationButtonContent>
            <telerik:RadRibbonView.Backstage>
                <telerik:RadRibbonBackstage>
                    <telerik:RadRibbonBackstageItem Header="Info" IsDefault="True">
                        <StackPanel Margin="30">
                            <TextBlock Text="Information" FontSize="38" Foreground="{telerik:Office2013Resource ResourceKey=AccentMainBrush}" />
                            <TextBlock Text="Lizensiert für ------" FontSize="30"/>
                            <TextBlock>VAS Software K. Martin <LineBreak/> Auf der Heide 34 <LineBreak/> 66440 Blieskastel <LineBreak/> Telefon: 0681-96719600</TextBlock>
                        </StackPanel>
                    </telerik:RadRibbonBackstageItem>

                    <telerik:RadRibbonBackstageItem Header="Einstellungen" IsDefault="False">
                        <Local:ConfigView/>
                    </telerik:RadRibbonBackstageItem>
               
                </telerik:RadRibbonBackstage>
            </telerik:RadRibbonView.Backstage>
 
        </telerik:RadRibbonView>
        <Grid Grid.Row="1" Margin="0,0,0,0">

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="9*"/>
                    <RowDefinition Height="1*"/>
                </Grid.RowDefinitions>
                <ScrollViewer Name="Scroller"  Grid.Row="0" Margin="0,0,0,0" Background="White">
                    <ItemsControl ItemsSource="{Binding Amount, Mode=OneWay}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding Path=.}" FontFamily="Consolas"/>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
                <TextBox Grid.Row="1" Text="{Binding ConsoleInput, Mode=TwoWay}" Background="#2a579a" Foreground="Black" FontFamily="Consolas" Name="InputBlock" BorderBrush="{x:Null}" SelectionBrush="{x:Null}" KeyDown="InputBlock_KeyDown" />
            </Grid>
        </Grid>


    </Grid>
</telerik:RadRibbonWindow>
