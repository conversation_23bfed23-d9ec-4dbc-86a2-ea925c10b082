using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Pipes;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using Telerik.Windows.Controls;
using VAS.Devices.CardTerminals;

namespace VASCard
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : RadRibbonWindow
    {
        RadDesktopAlertManager manager = new RadDesktopAlertManager();
        private System.Windows.Forms.NotifyIcon notifyIcon = null;
        private TemCom tem;

        static MainWindow()
        {
            RadRibbonWindow.IsWindowsThemeEnabled = false;
        }

        public MainWindow()
        {
            InitializeComponent();

            var listAlerts = new List<RadDesktopAlert>();
            this.ShowInTaskbar = false;
            notifyIcon = new System.Windows.Forms.NotifyIcon();
            notifyIcon.Click += new EventHandler(notifyIcon_Click);
            /*  notifyIcon.DoubleClick += new EventHandler(notifyIcon_DoubleClick);*/
            notifyIcon.Icon = Properties.Resources.ec4;
            /* Messenger.Default.Register<string>(this, "NewAmount", a =>
             {
                 Application.Current.Dispatcher.Invoke(DispatcherPriority.Loaded, new ThreadStart(delegate
                 {
                     this.Show();
                     this.Activate();

                 }));
             });
             Messenger.Default.Register<string>(this, "PaymentDone", a =>
             {
                 Thread.Sleep(3000);
                 Application.Current.Dispatcher.Invoke(DispatcherPriority.Background, new ThreadStart(delegate
                 {
                     this.Hide();

                 }));

             });*/
            Messenger.Default.Register<RadDesktopAlert>(this, "Alert", a =>
            {
                var alert = (RadDesktopAlert)a;

                alert.Click += Alert_Click;
                //  manager.CloseAllAlerts();
                manager.ShowAlert(a, false);


            });
            manager = new RadDesktopAlertManager(AlertScreenPosition.BottomRight);
            tem = new TemCom();
            try
            {
                tem.StartTemCom();
            }
            catch (Exception exp)
            {
                var para = new DialogParameters();

                RadWindow.Confirm(exp.Message + System.Environment.NewLine + "Soll das Programm neu gestartet werden?", this.OnError2);

            }
        }
        private void OnError(object sender, WindowClosedEventArgs e)
        {
            if (e.DialogResult == true)
            {
                tem.StopTemCom();

                System.Diagnostics.Process.Start(Application.ResourceAssembly.Location);
                Application.Current.Shutdown();
            }
            else
            {
                RadWindow.Alert("VAS EC Terminal wurde beendet.");
                Application.Current.Shutdown();
            }
        }
        private void OnError2(object sender, WindowClosedEventArgs e)
        {
            tem.StopTemCom();
            if (e.DialogResult == true)
            {
                Application.Current.Shutdown();
            }
            else
            {

                System.Diagnostics.Process.Start(Application.ResourceAssembly.Location);
                Application.Current.Shutdown();
            }
        }

        private void Alert_Click(object sender, Telerik.Windows.RadRoutedEventArgs e)
        {
            if (!this.IsVisible)
            {
                this.Show();
                this.Activate();
            }
        }

        private void notifyIcon_Click(object sender, EventArgs e)
        {
            if (this.IsVisible)
            {
                this.Hide();
            }
            else
            {
                this.Show();
                this.Activate();
            }
        }

        private void RadRibbonWindow_Loaded(object sender, RoutedEventArgs e)
        {
            notifyIcon.Visible = true;
        }

        private void InputBlock_KeyDown(object sender, KeyEventArgs e)
        {
            if ((e.Key == Key.Enter) && (InputBlock.Text.Length > 0))
            {
                if (InputBlock.Text[InputBlock.Text.Length - 1] == ';')
                {
                    ((VASCardModel)DataContext).ConsoleInput = InputBlock.Text;
                    ((VASCardModel)DataContext).RunCommand();
                    InputBlock.Focus();
                    Scroller.ScrollToBottom();
                }
            }

        }

        private void RadRibbonWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            e.Cancel = true;
            RadWindow.Confirm("Sind Sie sicher, dass das EC Terminal geschlossen werden soll?" + Environment.NewLine + "Es wären keine EC Zahlungen aus der Kasse mehr möglich.", this.OnError2);

        }

        private void ClosingButton_Click(object sender, RoutedEventArgs e)
        {
            ((VASCardModel)this.DataContext).ConsoleInput = "closeday;";
            ((VASCardModel)this.DataContext).RunCommand();
        }

        private void RestartButton_Click(object sender, RoutedEventArgs e)
        {
            ((VASCardModel)this.DataContext).ConsoleInput = "reset;";
            ((VASCardModel)this.DataContext).RunCommand();
        }
    }
}
