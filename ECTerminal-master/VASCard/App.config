<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="VASCard.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5.1" />
    </startup>
    <applicationSettings>
        <VASCard.Properties.Settings>
            <setting name="TerminalConfig" serializeAs="String">
                <value>&lt;?xml version="1.0" encoding="utf-16"?&gt; &lt;Config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&gt;   &lt;Transport&gt;Network&lt;/Transport&gt;   &lt;TransportSettings&gt;     &lt;Port&gt;COM20&lt;/Port&gt;     &lt;BaudRate&gt;9600&lt;/BaudRate&gt;     &lt;StopBits&gt;One&lt;/StopBits&gt;     &lt;RemoteIP&gt;*************&lt;/RemoteIP&gt;     &lt;RemotePort&gt;22000&lt;/RemotePort&gt;   &lt;/TransportSettings&gt;   &lt;RegistrationCommand /&gt; &lt;/Config&gt;</value>
            </setting>
        </VASCard.Properties.Settings>
    </applicationSettings>
</configuration>