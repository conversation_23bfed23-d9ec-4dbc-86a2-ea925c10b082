using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace VASCard
{
    /// <summary>
    /// Interaction logic for ConfigView.xaml
    /// </summary>
    public partial class ConfigView : UserControl
    {
        public ConfigView()
        {
            InitializeComponent();
          

        }

        private void RadPropertyGrid_LostFocus(object sender, RoutedEventArgs e)
        {
            Properties.Settings.Default.Save();
            Properties.Settings.Default.Upgrade();
        }

        private void RadPropertyGrid_EditEnded(object sender, Telerik.Windows.Controls.Data.PropertyGrid.PropertyGridEditEndedEventArgs e)
        {
            Properties.Settings.Default.Save();
            Properties.Settings.Default.Upgrade();
        }
    }
}
