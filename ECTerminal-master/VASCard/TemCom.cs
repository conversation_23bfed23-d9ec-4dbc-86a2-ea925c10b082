using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using VAS.Devices.CardTerminals.Protocols.ZVT.TransportLayer;
using VAS.Diagnostic.Log;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer;
using System.IO.Ports;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer.Commands;
using VAS.Devices.CardTerminals.Protocols.ZVT.ApplicationLayer.Parameters;
using System.Windows.Forms;
using VAS.Devices.CardTerminals.Commands;
using System.Collections;
using System.Web;
using System.Xml;
using System.Xml.Linq;
using Telerik.Windows.Controls;
using GalaSoft.MvvmLight.Messaging;

namespace VASCard
{
    public class TemCom
    {

        public static string price = "0";
        public static string _paymentSettings = null;
        public static string _configuration = null;
        private HttpListener Listener;
        public static bool status;
        private bool RunningPayment;

        public TemCom()
        {

        }
        public void StartTemCom()
        {
            RunningPayment = false;
            Listener = new HttpListener();
            Listener.Prefixes.Add("http://+:1000/");
            Listener.Start();
            Listener.BeginGetContext(new AsyncCallback(this.ListenerCallback), Listener);
            status = false;
        }
        public void StopTemCom()
        {
            Listener.Abort();
        }

        static void environment_StatusReceived(IntermediateStatus status)
        {

            RadWindow.Alert(status.StatusText);
        }

        private void ListenerCallback(IAsyncResult Result)
        {

            HttpListenerContext Context;

            try
            {
                Context = Listener.EndGetContext(Result);
                this.ProcessRequest(Context);

            }
#pragma warning disable CS0168 // Die Variable "exDi" ist deklariert, wird aber nie verwendet.
            catch (ObjectDisposedException exDi)
#pragma warning restore CS0168 // Die Variable "exDi" ist deklariert, wird aber nie verwendet.
            {
            }
            catch (HttpListenerException eR)
            {

                System.Diagnostics.Debug.WriteLine(eR.ToString());

            }
            try
            {

                Listener.BeginGetContext(new AsyncCallback(ListenerCallback), Listener);

            }
            catch (InvalidOperationException eR)
            {

                System.Diagnostics.Debug.WriteLine(eR.ToString());

            }
            

        }

        public void ContextResponse(HttpListenerContext context, string response)
        {
            byte[] bytes = System.Text.UTF8Encoding.Default.GetBytes(response);
            BitArray bits = new System.Collections.BitArray(bytes);
            context.Response.OutputStream.Write(bytes, 0, bytes.Length);
        }

        private void ProcessRequest(HttpListenerContext context)
        {
            try
            {
                if (RunningPayment == true)
                {
                    ContextResponse(context, "Gerät ist beschäfigt.");
                }
                else
                {
                    HttpListenerRequest Request = context.Request;
                    string[] words = Request.Url.ToString().Split('/');

                    if (words.Length >= 4)
                    {
                        var parsed = HttpUtility.ParseQueryString(words[3]);
                        string costs = parsed["ec"];

                        if (costs != null)
                        {
                            Messenger.Default.Send<string>(costs, "NewAmount");
                            Messenger.Default.Send<string>("EC Zahlung " + String.Format("{0:0.00}", Double.Parse(costs) / 100) + " €", "Payload");

                            ContextResponse(context, PipeClass.paymentCall(costs, Properties.Settings.Default.TerminalConfig));
                            Messenger.Default.Send<string>("", "PaymentDone");
                        }
                        context.Response.Close();

                    }
                }
            }
            catch
            {
                ContextResponse(context, "Fehler beim Verarbeiten. Möglicherweise Gerät nicht bereit. (StatusCode 63)");
                Messenger.Default.Send<string>("", "PaymentDone");
                context.Response.Close();
            }


        }


    }
}
