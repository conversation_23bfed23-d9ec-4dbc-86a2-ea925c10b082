using GalaSoft.MvvmLight.Messaging;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Telerik.Windows.Controls;

namespace VASCard
{
    public class VASCardModel : ViewModelBase, INotifyPropertyChanged
    {

        private VAS.Devices.CardTerminals.Config _Config;
        private string _Status;

        private ObservableCollection<string> _Amount;
        string consoleInput = string.Empty;


        public string ConsoleInput
        {
            get
            {
                return consoleInput;
            }
            set
            {
                consoleInput = value;
                OnPropertyChanged("ConsoleInput");
            }
        }
        public VAS.Devices.CardTerminals.Config Config
        {

            get { return _Config; }
            set
            {
                if (value != _Config)
                {
                    _Config = value;
                    OnPropertyChanged("Config");
                }
            }
        }
        public ObservableCollection<string> Amount
        {
            get { return _Amount; }
            set
            {
                if (value != _Amount)
                {
                    _Amount = value;
                    OnPropertyChanged("Amount");
                }
            }
        }
        public string Status
        {
            get { return _Status; }
            set
            {
                if (value != _Status)
                {
                    _Status = value;
                    OnPropertyChanged("Status");
                }
            }
        }
        public VASCardModel()
        {
            /*  var obj = "<Config>  <Transport>Network</Transport>  <!--<Transport>Seriel</Transport>-->  <!-- <TransportSettings> <Port>COM07</Port>                <BaudRate>9600</BaudRate>                <StopBits>One</StopBits> </TransportSettings>-->  <TransportSettings>" +
  "<RemoteIP>***************</RemoteIP>" +
  "<RemotePort>22000</RemotePort>" +
  " </TransportSettings>" +

  " <RegistrationCommand>" +
  "  <ECRPrintsAdministrationReceipts>False</ECRPrintsAdministrationReceipts>" +
  " <ECRPrintsPaymentReceipt>False</ECRPrintsPaymentReceipt>" +
  "<PTDisableAmountInput>False</PTDisableAmountInput>" +
  "<PTDisableAdministrationFunctions>False</PTDisableAdministrationFunctions>" +
  "</RegistrationCommand>" +
  "</Config>";
         
                System.Xml.Serialization.XmlSerializer ser = new System.Xml.Serialization.XmlSerializer(typeof(VAS.Devices.CardTerminals.Config));

                using (StringReader sr = new StringReader(obj))
                {
                    _Config = (VAS.Devices.CardTerminals.Config)ser.Deserialize(sr);
                }*/
            _Config = Properties.Settings.Default.TerminalConfig;
            /*    Properties.Settings.Default.TerminalConfig = _Config;
                Properties.Settings.Default.Save();*/

            Amount = new ObservableCollection<string>() { "Warte auf Zahlungsaufforderung..." };
            Status = "";
            Messenger.Default.Register<string>(this, "Payload", a =>
            {
                Application.Current.Dispatcher.Invoke(System.Windows.Threading.DispatcherPriority.Background, new System.Threading.ThreadStart(delegate
                {
                    var line = DateTime.Now.ToString("hh:mm:ss") + ":\t" + a;
                    Amount.Add(line);
                }));
            });
            Messenger.Default.Register<string>(this, "PaymentDone", a =>
            {
                Application.Current.Dispatcher.Invoke(DispatcherPriority.Background, new ThreadStart(delegate
                {
                    var line = " ------------------------------------------------------- ";
                    Amount.Add(line);

                }));
            });
        }

        internal void RunCommand()
        {
            Application.Current.Dispatcher.Invoke(DispatcherPriority.Background, new ThreadStart(delegate
            {
                Amount.Add(consoleInput);

            }));

            if (consoleInput.Contains("pay"))
            {

                var result = new String(consoleInput.Where(Char.IsDigit).ToArray());
                Task task = new Task(() =>
                {
                    try
                    {
                        PipeClass.paymentCall(result, _Config);
                    }
                    catch (Exception exp)
                    {
                        Messenger.Default.Send<string>(exp.Message, "Payload");
                    }
                });
                task.Start();
            }
            else if (consoleInput.Equals("closeday;"))
            {
                try
                {
                    PipeClass.createClosing();
                }
                catch (Exception exp)
                {
                    Messenger.Default.Send<string>(exp.Message, "Payload");
                }
            }
            else if (consoleInput.Equals("reset;"))
            {
                try
                {
                    PipeClass.createClosing2();
                }
                catch (Exception exp)
                {
                    Messenger.Default.Send<string>(exp.Message, "Payload");
                }
            }
            else if (consoleInput.Equals("exit;"))
            {
                try
                {
                    System.Windows.Application.Current.Shutdown();
                }
                catch (Exception exp)
                {
                    Messenger.Default.Send<string>(exp.Message, "Payload");
                }
            }
            ConsoleInput = String.Empty;
        }
    }
}
